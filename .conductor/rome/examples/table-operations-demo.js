#!/usr/bin/env node

/**
 * NocoBase 表格操作工具演示
 * 
 * 本示例演示如何使用新增的表格操作工具来：
 * 1. 为表格添加各种操作按钮
 * 2. 配置表格列显示
 * 3. 设置表格筛选和排序
 * 4. 发送自定义请求
 */

import { NocoBaseClient } from '../dist/client.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'your-token-here',
  app: 'your-app-name'
});

async function demonstrateTableOperations() {
  console.log('🎯 NocoBase 表格操作工具演示\n');

  // 假设我们有一个表格区块的 UID
  const tableUid = 'your-table-uid-here';

  try {
    // 1. 添加基础操作按钮
    console.log('1️⃣ 添加基础操作按钮...');
    
    // 添加新增按钮
    await client.addTableAction(tableUid, {
      title: "{{t('Add new')}}",
      action: 'create',
      icon: 'PlusOutlined',
      type: 'primary',
      align: 'right',
      requiresACL: true,
      aclAction: 'create'
    });
    console.log('✅ 新增按钮添加成功');

    // 添加筛选按钮
    await client.addTableAction(tableUid, {
      title: "{{t('Filter')}}",
      action: 'filter',
      icon: 'FilterOutlined',
      align: 'left',
      requiresACL: false
    });
    console.log('✅ 筛选按钮添加成功');

    // 添加批量删除按钮
    await client.addTableAction(tableUid, {
      title: "{{t('Delete selected')}}",
      action: 'destroy',
      icon: 'DeleteOutlined',
      align: 'right',
      requiresACL: true,
      aclAction: 'destroyMany'
    });
    console.log('✅ 批量删除按钮添加成功');

    // 2. 添加自定义操作按钮
    console.log('\n2️⃣ 添加自定义操作按钮...');
    
    await client.addTableAction(tableUid, {
      title: "{{t('Send Email')}}",
      action: 'sendEmail',
      icon: 'MailOutlined',
      type: 'default',
      align: 'right',
      requiresACL: true,
      aclAction: 'sendEmail',
      componentProps: {
        confirm: {
          title: "{{t('Send email to selected users?')}}",
          content: "{{t('This action will send emails to all selected users.')}}"
        }
      }
    });
    console.log('✅ 自定义邮件发送按钮添加成功');

    // 3. 配置表格列
    console.log('\n3️⃣ 配置表格列...');
    
    // 配置用户名列
    await client.insertAdjacentSchema(tableUid, {
      type: 'void',
      'x-decorator': 'TableV2.Column.Decorator',
      'x-component': 'TableV2.Column',
      properties: {
        username: {
          type: 'string',
          'x-component': 'CollectionField',
          'x-read-pretty': true,
          title: '用户名'
        }
      },
      title: '用户名',
      width: 150,
      fixed: 'left'
    });
    console.log('✅ 用户名列配置成功');

    // 配置邮箱列
    await client.insertAdjacentSchema(tableUid, {
      type: 'void',
      'x-decorator': 'TableV2.Column.Decorator',
      'x-component': 'TableV2.Column',
      properties: {
        email: {
          type: 'string',
          'x-component': 'Input.Email',
          'x-read-pretty': true,
          title: '邮箱'
        }
      },
      title: '邮箱地址',
      width: 200,
      sortable: true,
      filterable: true
    });
    console.log('✅ 邮箱列配置成功');

    // 4. 配置表格筛选器
    console.log('\n4️⃣ 配置表格筛选器...');
    
    await client.patchSchema(tableUid, {
      'x-decorator-props': {
        params: {
          filter: {
            status: 'active' // 默认只显示活跃用户
          }
        }
      },
      'x-enable-quick-filter': true,
      'x-quick-filter-fields': ['username', 'email', 'nickname']
    });
    console.log('✅ 表格筛选器配置成功');

    // 5. 配置表格排序
    console.log('\n5️⃣ 配置表格排序...');
    
    await client.patchSchema(tableUid, {
      'x-decorator-props': {
        params: {
          sort: ['-createdAt'] // 默认按创建时间倒序
        },
        dragSort: false // 禁用拖拽排序
      }
    });
    console.log('✅ 表格排序配置成功');

    // 6. 发送自定义请求示例
    console.log('\n6️⃣ 发送自定义请求示例...');
    
    try {
      const result = await client.sendCustomRequest('export-users', {
        currentRecord: null,
        selectedRecords: [],
        formData: {
          format: 'xlsx',
          includeInactive: false
        }
      });
      console.log('✅ 自定义请求发送成功：', result);
    } catch (error) {
      console.log('⚠️ 自定义请求示例跳过（需要实际的请求配置）');
    }

    console.log('\n🎉 表格操作工具演示完成！');
    console.log('\n📝 总结：');
    console.log('- ✅ 添加了新增、筛选、批量删除等基础操作按钮');
    console.log('- ✅ 添加了自定义邮件发送操作按钮');
    console.log('- ✅ 配置了用户名和邮箱列的显示');
    console.log('- ✅ 设置了默认筛选条件和快速筛选');
    console.log('- ✅ 配置了默认排序规则');
    console.log('- ✅ 演示了自定义请求的发送');

  } catch (error) {
    console.error('❌ 演示过程中出现错误：', error.message);
    console.error('💡 提示：请确保提供正确的表格 UID 和 NocoBase 配置');
  }
}

// 使用说明
console.log(`
📖 使用说明：

1. 修改配置：
   - 更新 NocoBase 客户端配置（baseUrl, token, app）
   - 替换 tableUid 为实际的表格区块 UID

2. 运行演示：
   node examples/table-operations-demo.js

3. 工具功能：
   - add_table_action: 添加表格操作按钮
   - remove_table_action: 删除表格操作按钮
   - update_table_action: 更新表格操作按钮
   - list_table_actions: 列出表格操作按钮
   - configure_table_column: 配置表格列
   - configure_table_filter: 配置表格筛选器
   - configure_table_sort: 配置表格排序
   - send_custom_request: 发送自定义请求

4. 更多信息：
   - 查看 docs/table-operations/TABLE_OPERATION_TOOLS.md
   - 查看 docs/table-operations/SWAGGER_API_ANALYSIS.md
`);

// 如果直接运行此文件，则执行演示
if (import.meta.url === `file://${process.argv[1]}`) {
  demonstrateTableOperations().catch(console.error);
}
