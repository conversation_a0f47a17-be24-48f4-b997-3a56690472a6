---
type: "always_apply"
---

## 如何启动测试环境的mcp
```shell
npm run start -- --base-url "https://app.dev.orb.local/api" --token "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA" --app "mcp_playground"
```

## 测试步骤
1. 启动测试环境的mcp
2. 查询工具能力，找到想匹配的能力
3. 根据测试需求和对应能力规划测试任务步骤，然后逐个使用mcp能力执行测试任务
4. 当遇到mcp无法完成的任务，则中断测试并记录问题