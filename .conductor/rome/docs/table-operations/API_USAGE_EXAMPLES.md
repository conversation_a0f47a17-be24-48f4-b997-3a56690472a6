# NocoBase 表格控件 API 使用示例

## 概述

本文档提供 NocoBase 表格控件的各种 API 使用示例，帮助开发者快速上手和实现自定义功能。

## 1. 基础表格配置

### 1.1 简单表格

```typescript
// 创建一个基础表格
const tableSchema = {
  type: 'void',
  name: 'userTable',
  'x-decorator': 'TableBlockProvider',
  'x-decorator-props': {
    collection: 'users',
    resource: 'users',
    action: 'list',
    params: {
      pageSize: 20,
    },
  },
  'x-component': 'CardItem',
  properties: {
    actions: {
      type: 'void',
      'x-component': 'Table.ActionBar',
      'x-component-props': {
        style: {
          marginBottom: 16,
        },
      },
      'x-initializer': 'tableActionInitializers',
    },
    content: {
      type: 'void',
      'x-component': 'TableV2',
      'x-use-decorator-props': true,
      properties: {
        id: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: 'ID',
          'x-collection-field': 'users.id',
          'x-component-props': {
            width: 100,
          },
        },
        name: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '姓名',
          'x-collection-field': 'users.name',
          'x-component-props': {
            width: 150,
          },
        },
        email: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '邮箱',
          'x-collection-field': 'users.email',
          'x-component-props': {
            width: 200,
          },
        },
        actions: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '操作',
          'x-component-props': {
            width: 150,
            fixed: 'right',
          },
          properties: {
            actions: {
              type: 'void',
              'x-component': 'Table.Column.ActionBar',
              'x-initializer': 'tableRecordActionInitializers',
            },
          },
        },
      },
    },
  },
};
```

### 1.2 带筛选的表格

```typescript
const filteredTableSchema = {
  type: 'void',
  name: 'filteredUserTable',
  'x-decorator': 'TableBlockProvider',
  'x-decorator-props': {
    collection: 'users',
    resource: 'users',
    action: 'list',
    params: {
      pageSize: 20,
      filter: {
        status: 'active',
      },
    },
  },
  'x-component': 'CardItem',
  properties: {
    actions: {
      type: 'void',
      'x-component': 'Table.ActionBar',
      'x-component-props': {
        style: {
          marginBottom: 16,
        },
      },
      properties: {
        filter: {
          type: 'void',
          title: '{{ t("Filter") }}',
          'x-action': 'filter',
          'x-component': 'Table.Filter',
          'x-settings': 'actionSettings:filter',
          'x-component-props': {
            filterTargets: ['name', 'email', 'status'],
          },
        },
        create: {
          type: 'void',
          title: '{{ t("Add new") }}',
          'x-action': 'create',
          'x-acl-action': 'create',
          'x-component': 'Action',
          'x-settings': 'actionSettings:addNew',
          'x-component-props': {
            type: 'primary',
            icon: 'PlusOutlined',
          },
        },
      },
    },
    // ... 表格内容配置
  },
};
```

## 2. 表格操作配置

### 2.1 新增操作

```typescript
const createActionSchema = {
  type: 'void',
  title: '{{ t("Add new") }}',
  'x-action': 'create',
  'x-acl-action': 'create',
  'x-component': 'Action',
  'x-settings': 'actionSettings:addNew',
  'x-component-props': {
    type: 'primary',
    icon: 'PlusOutlined',
    openMode: 'drawer', // 或 'modal'
  },
  properties: {
    drawer: {
      type: 'void',
      title: '{{ t("Add new record") }}',
      'x-component': 'Action.Container',
      'x-component-props': {
        className: 'nb-action-popup',
      },
      properties: {
        tabs: {
          type: 'void',
          'x-component': 'Tabs',
          'x-component-props': {},
          'x-initializer': 'popup:addTab',
          properties: {
            tab1: {
              type: 'void',
              title: '{{t("Create")}}',
              'x-component': 'Tabs.TabPane',
              'x-designer': 'Tabs.Designer',
              properties: {
                grid: {
                  type: 'void',
                  'x-component': 'Grid',
                  'x-initializer': 'popup:common:addBlock',
                  properties: {},
                },
              },
            },
          },
        },
      },
    },
  },
};
```

### 2.2 编辑操作

```typescript
const updateActionSchema = {
  type: 'void',
  title: '{{ t("Edit") }}',
  'x-action': 'update',
  'x-acl-action': 'update',
  'x-component': 'Action.Link',
  'x-settings': 'actionSettings:edit',
  'x-component-props': {
    icon: 'EditOutlined',
    openMode: 'drawer',
  },
  properties: {
    drawer: {
      type: 'void',
      title: '{{ t("Edit record") }}',
      'x-component': 'Action.Container',
      properties: {
        form: {
          type: 'void',
          'x-component': 'Form',
          'x-use-decorator-props': true,
          properties: {
            // 表单字段配置
            name: {
              type: 'string',
              title: '姓名',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              required: true,
            },
            email: {
              type: 'string',
              title: '邮箱',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                type: 'email',
              },
            },
          },
        },
      },
    },
  },
};
```

### 2.3 删除操作

```typescript
const deleteActionSchema = {
  type: 'void',
  title: '{{ t("Delete") }}',
  'x-action': 'destroy',
  'x-acl-action': 'destroy',
  'x-component': 'Action',
  'x-use-component-props': 'useDestroyActionProps',
  'x-settings': 'actionSettings:delete',
  'x-component-props': {
    icon: 'DeleteOutlined',
    confirm: {
      title: "{{t('Delete record')}}",
      content: "{{t('Are you sure you want to delete it?')}}",
    },
    refreshDataBlockRequest: true,
  },
  'x-action-settings': {
    triggerWorkflows: [],
  },
};
```

### 2.4 查看操作

```typescript
const viewActionSchema = {
  type: 'void',
  title: '{{ t("View") }}',
  'x-action': 'view',
  'x-component': 'Action.Link',
  'x-settings': 'actionSettings:view',
  'x-component-props': {
    openMode: 'drawer',
  },
  properties: {
    drawer: {
      type: 'void',
      title: '{{ t("View record") }}',
      'x-component': 'Action.Container',
      properties: {
        tabs: {
          type: 'void',
          'x-component': 'Tabs',
          properties: {
            tab1: {
              type: 'void',
              title: '{{t("Details")}}',
              'x-component': 'Tabs.TabPane',
              properties: {
                grid: {
                  type: 'void',
                  'x-component': 'Grid',
                  'x-initializer': 'popup:common:addBlock',
                  properties: {},
                },
              },
            },
          },
        },
      },
    },
  },
};
```

## 3. 字段配置示例

### 3.1 基础字段配置

```typescript
const nameColumnSchema = {
  type: 'void',
  'x-component': 'TableV2.Column',
  title: '姓名',
  'x-collection-field': 'users.name',
  'x-toolbar': 'TableColumnSchemaToolbar',
  'x-settings': 'fieldSettings:TableColumn',
  'x-component-props': {
    width: 150,
    fixed: 'left',
    sorter: true,
    tooltip: '用户姓名',
  },
};
```

### 3.2 关联字段配置

```typescript
const departmentColumnSchema = {
  type: 'void',
  'x-component': 'TableV2.Column',
  title: '部门',
  'x-collection-field': 'users.department',
  'x-toolbar': 'TableColumnSchemaToolbar',
  'x-settings': 'fieldSettings:TableColumn',
  'x-component-props': {
    width: 200,
    sorter: true,
  },
  properties: {
    department: {
      type: 'void',
      'x-component': 'CollectionField',
      'x-collection-field': 'users.department',
      'x-decorator': 'FormItem',
      'x-component': 'Select',
      'x-component-props': {
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    },
  },
};
```

### 3.3 自定义字段配置

```typescript
const customStatusColumnSchema = {
  type: 'void',
  'x-component': 'TableV2.Column',
  title: '状态',
  'x-collection-field': 'users.status',
  'x-toolbar': 'TableColumnSchemaToolbar',
  'x-settings': 'fieldSettings:TableColumn',
  'x-component-props': {
    width: 120,
  },
  properties: {
    status: {
      type: 'void',
      'x-component': 'CollectionField',
      'x-collection-field': 'users.status',
      'x-decorator': 'FormItem',
      'x-component': 'Select',
      'x-component-props': {
        options: [
          { label: '激活', value: 'active', color: 'green' },
          { label: '禁用', value: 'inactive', color: 'red' },
          { label: '待审核', value: 'pending', color: 'orange' },
        ],
      },
    },
  },
};
```

## 4. 高级配置示例

### 4.1 树形表格

```typescript
const treeTableSchema = {
  type: 'void',
  name: 'categoryTree',
  'x-decorator': 'TableBlockProvider',
  'x-decorator-props': {
    collection: 'categories',
    resource: 'categories',
    action: 'list',
    params: {
      tree: true,
      sort: {
        sortBy: 'order',
        order: 'asc',
      },
    },
  },
  'x-component': 'CardItem',
  properties: {
    actions: {
      type: 'void',
      'x-component': 'Table.ActionBar',
      properties: {
        expand: {
          type: 'void',
          title: '{{ t("Expand/Collapse") }}',
          'x-action': 'toggle',
          'x-component': 'Action',
          'x-component-props': {
            icon: 'ExpandOutlined',
          },
        },
        create: {
          type: 'void',
          title: '{{ t("Add new") }}',
          'x-action': 'create',
          'x-acl-action': 'create',
          'x-component': 'Action',
          'x-component-props': {
            type: 'primary',
            icon: 'PlusOutlined',
          },
        },
      },
    },
    content: {
      type: 'void',
      'x-component': 'TableV2',
      'x-use-decorator-props': true,
      'x-component-props': {
        rowKey: 'id',
        childrenColumnName: 'children',
        expandable: true,
      },
      properties: {
        // ... 列配置
        actions: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '操作',
          'x-component-props': {
            width: 200,
            fixed: 'right',
          },
          properties: {
            actions: {
              type: 'void',
              'x-component': 'Table.Column.ActionBar',
              properties: {
                createChild: {
                  type: 'void',
                  title: '{{ t("Add child") }}',
                  'x-action': 'create',
                  'x-acl-action': 'create',
                  'x-component': 'Action.Link',
                  'x-component-props': {
                    icon: 'PlusCircleOutlined',
                  },
                },
                // ... 其他操作
              },
            },
          },
        },
      },
    },
  },
};
```

### 4.2 可编辑表格

```typescript
const editableTableSchema = {
  type: 'void',
  name: 'editableTable',
  'x-decorator': 'TableBlockProvider',
  'x-decorator-props': {
    collection: 'products',
    resource: 'products',
    action: 'list',
  },
  'x-component': 'CardItem',
  properties: {
    actions: {
      type: 'void',
      'x-component': 'Table.ActionBar',
      properties: {
        save: {
          type: 'void',
          title: '{{ t("Save") }}',
          'x-action': 'customize:table:request:global',
          'x-component': 'Action',
          'x-component-props': {
            type: 'primary',
            icon: 'SaveOutlined',
          },
        },
      },
    },
    content: {
      type: 'void',
      'x-component': 'TableV2',
      'x-use-decorator-props': true,
      properties: {
        name: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '产品名称',
          'x-collection-field': 'products.name',
          'x-component-props': {
            width: 200,
          },
          properties: {
            name: {
              type: 'string',
              'x-component': 'CollectionField',
              'x-collection-field': 'products.name',
              'x-decorator': 'QuickEdit',
              'x-decorator-props': {
                mode: 'inline',
              },
              'x-component': 'Input',
            },
          },
        },
        price: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '价格',
          'x-collection-field': 'products.price',
          'x-component-props': {
            width: 120,
          },
          properties: {
            price: {
              type: 'number',
              'x-component': 'CollectionField',
              'x-collection-field': 'products.price',
              'x-decorator': 'QuickEdit',
              'x-decorator-props': {
                mode: 'inline',
              },
              'x-component': 'InputNumber',
            },
          },
        },
      },
    },
  },
};
```

### 4.3 批量操作

```typescript
const bulkActionsSchema = {
  type: 'void',
  'x-component': 'Table.ActionBar',
  properties: {
    bulkDelete: {
      type: 'void',
      title: '{{ t("Delete selected") }}',
      'x-action': 'destroy',
      'x-acl-action': 'destroyMany',
      'x-component': 'Action',
      'x-component-props': {
        icon: 'DeleteOutlined',
        confirm: {
          title: "{{t('Delete selected records')}}",
          content: "{{t('Are you sure you want to delete the selected records?')}}",
        },
      },
      'x-use-component-props': 'useBulkDestroyActionProps',
    },
    bulkUpdate: {
      type: 'void',
      title: '{{ t("Update selected") }}',
      'x-action': 'customize:table:request:global',
      'x-component': 'Action',
      'x-component-props': {
        icon: 'EditOutlined',
      },
      properties: {
        drawer: {
          type: 'void',
          title: '{{ t("Batch update") }}',
          'x-component': 'Action.Container',
          properties: {
            form: {
              type: 'void',
              'x-component': 'Form',
              properties: {
                status: {
                  type: 'string',
                  title: '状态',
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    options: [
                      { label: '激活', value: 'active' },
                      { label: '禁用', value: 'inactive' },
                    ],
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
```

## 5. 自定义操作示例

### 5.1 自定义导出操作

```typescript
const exportActionSchema = {
  type: 'void',
  title: '{{ t("Export") }}',
  'x-action': 'customize:table:request:global',
  'x-component': 'Action',
  'x-component-props': {
    icon: 'DownloadOutlined',
  },
  'x-use-component-props': 'useExportActionProps',
};

// 自定义操作 Hook
export const useExportActionProps = () => {
  const { resource } = useBlockResourceContext();
  const { t } = useTranslation();
  
  return {
    async onClick() {
      try {
        const data = await resource.export({});
        const blob = new Blob([data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'export.csv';
        a.click();
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Export failed:', error);
      }
    },
  };
};
```

### 5.2 自定义导入操作

```typescript
const importActionSchema = {
  type: 'void',
  title: '{{ t("Import") }}',
  'x-action': 'customize:table:request:global',
  'x-component': 'Action',
  'x-component-props': {
    icon: 'UploadOutlined',
  },
  properties: {
    drawer: {
      type: 'void',
      title: '{{ t("Import data") }}',
      'x-component': 'Action.Container',
      properties: {
        form: {
          type: 'void',
          'x-component': 'Form',
          properties: {
            file: {
              type: 'string',
              title: '选择文件',
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              'x-component-props': {
                accept: '.csv,.xlsx',
              },
            },
          },
        },
      },
    },
  },
};
```

## 6. 权限控制示例

### 6.1 基于角色的权限控制

```typescript
const roleBasedActions = {
  type: 'void',
  'x-component': 'Table.ActionBar',
  properties: {
    create: {
      type: 'void',
      title: '{{ t("Add new") }}',
      'x-action': 'create',
      'x-acl-action': 'create',
      'x-component': 'Action',
      'x-decorator': 'ACLActionProvider',
      'x-component-props': {
        type: 'primary',
        icon: 'PlusOutlined',
      },
    },
    approve: {
      type: 'void',
      title: '{{ t("Approve") }}',
      'x-action': 'approve',
      'x-acl-action': 'approve',
      'x-component': 'Action',
      'x-decorator': 'ACLActionProvider',
      'x-component-props': {
        icon: 'CheckOutlined',
      },
      useVisible: () => {
        const { user } = useCurrentUser();
        return user?.role === 'admin' || user?.role === 'manager';
      },
    },
  },
};
```

### 6.2 数据级权限控制

```typescript
const dataLevelPermissions = {
  type: 'void',
  'x-component': 'Table.ActionBar',
  properties: {
    edit: {
      type: 'void',
      title: '{{ t("Edit") }}',
      'x-action': 'update',
      'x-acl-action': 'update',
      'x-component': 'Action.Link',
      'x-decorator': 'ACLActionProvider',
      'x-component-props': {
        icon: 'EditOutlined',
      },
      useVisible: (record) => {
        const { user } = useCurrentUser();
        return user?.id === record.createdById || user?.role === 'admin';
      },
    },
    delete: {
      type: 'void',
      title: '{{ t("Delete") }}',
      'x-action': 'destroy',
      'x-acl-action': 'destroy',
      'x-component': 'Action',
      'x-decorator': 'ACLActionProvider',
      'x-component-props': {
        icon: 'DeleteOutlined',
      },
      useVisible: (record) => {
        const { user } = useCurrentUser();
        return user?.id === record.createdById || user?.role === 'admin';
      },
    },
  },
};
```

## 7. 完整示例

### 7.1 完整的用户管理表格

```typescript
const userManagementTable = {
  type: 'void',
  name: 'userManagement',
  'x-decorator': 'TableBlockProvider',
  'x-decorator-props': {
    collection: 'users',
    resource: 'users',
    action: 'list',
    params: {
      pageSize: 20,
      sort: {
        sortBy: 'createdAt',
        order: 'desc',
      },
    },
  },
  'x-component': 'CardItem',
  properties: {
    actions: {
      type: 'void',
      'x-component': 'Table.ActionBar',
      'x-component-props': {
        style: {
          marginBottom: 16,
        },
      },
      properties: {
        filter: {
          type: 'void',
          title: '{{ t("Filter") }}',
          'x-action': 'filter',
          'x-component': 'Table.Filter',
          'x-settings': 'actionSettings:filter',
          'x-component-props': {
            filterTargets: ['name', 'email', 'status'],
          },
        },
        create: {
          type: 'void',
          title: '{{ t("Add new") }}',
          'x-action': 'create',
          'x-acl-action': 'create',
          'x-component': 'Action',
          'x-settings': 'actionSettings:addNew',
          'x-component-props': {
            type: 'primary',
            icon: 'PlusOutlined',
          },
        },
        export: {
          type: 'void',
          title: '{{ t("Export") }}',
          'x-action': 'customize:table:request:global',
          'x-component': 'Action',
          'x-component-props': {
            icon: 'DownloadOutlined',
          },
          'x-use-component-props': 'useExportActionProps',
        },
      },
    },
    content: {
      type: 'void',
      'x-component': 'TableV2',
      'x-use-decorator-props': true,
      properties: {
        id: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: 'ID',
          'x-collection-field': 'users.id',
          'x-component-props': {
            width: 80,
          },
        },
        avatar: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '头像',
          'x-collection-field': 'users.avatar',
          'x-component-props': {
            width: 80,
          },
        },
        name: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '姓名',
          'x-collection-field': 'users.name',
          'x-component-props': {
            width: 150,
            fixed: 'left',
            sorter: true,
          },
        },
        email: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '邮箱',
          'x-collection-field': 'users.email',
          'x-component-props': {
            width: 200,
            sorter: true,
          },
        },
        department: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '部门',
          'x-collection-field': 'users.department',
          'x-component-props': {
            width: 150,
          },
        },
        status: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '状态',
          'x-collection-field': 'users.status',
          'x-component-props': {
            width: 100,
          },
          properties: {
            status: {
              type: 'void',
              'x-component': 'CollectionField',
              'x-collection-field': 'users.status',
              'x-decorator': 'FormItem',
              'x-component': 'Select',
              'x-component-props': {
                options: [
                  { label: '激活', value: 'active', color: 'green' },
                  { label: '禁用', value: 'inactive', color: 'red' },
                ],
              },
            },
          },
        },
        createdAt: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '创建时间',
          'x-collection-field': 'users.createdAt',
          'x-component-props': {
            width: 180,
            sorter: true,
          },
        },
        actions: {
          type: 'void',
          'x-component': 'TableV2.Column',
          title: '操作',
          'x-component-props': {
            width: 200,
            fixed: 'right',
          },
          properties: {
            actions: {
              type: 'void',
              'x-component': 'Table.Column.ActionBar',
              properties: {
                view: {
                  type: 'void',
                  title: '{{ t("View") }}',
                  'x-action': 'view',
                  'x-component': 'Action.Link',
                  'x-settings': 'actionSettings:view',
                  'x-component-props': {
                    icon: 'EyeOutlined',
                  },
                },
                edit: {
                  type: 'void',
                  title: '{{ t("Edit") }}',
                  'x-action': 'update',
                  'x-acl-action': 'update',
                  'x-component': 'Action.Link',
                  'x-settings': 'actionSettings:edit',
                  'x-component-props': {
                    icon: 'EditOutlined',
                  },
                },
                delete: {
                  type: 'void',
                  title: '{{ t("Delete") }}',
                  'x-action': 'destroy',
                  'x-acl-action': 'destroy',
                  'x-component': 'Action',
                  'x-settings': 'actionSettings:delete',
                  'x-component-props': {
                    icon: 'DeleteOutlined',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
```

## 8. 最佳实践

### 8.1 性能优化

```typescript
// 1. 使用虚拟滚动
'x-component-props': {
  scroll: {
    y: 600,
    x: 1200,
  },
}

// 2. 合理设置分页
params: {
  pageSize: 20,
  page: 1,
}

// 3. 优化列宽
'x-component-props': {
  width: 150,
  ellipsis: true,
}

// 4. 使用固定列
'x-component-props': {
  fixed: 'left',
}
```

### 8.2 用户体验优化

```typescript
// 1. 添加加载状态
'x-component-props': {
  loading: true,
}

// 2. 自定义空状态
'x-component-props': {
  locale: {
    emptyText: '暂无数据',
  },
}

// 3. 添加工具提示
'x-component-props': {
  tooltip: '点击查看详情',
}

// 4. 使用图标增强视觉效果
'x-component-props': {
  icon: 'UserOutlined',
}
```

这些示例涵盖了 NocoBase 表格控件的主要使用场景，开发者可以根据具体需求进行调整和扩展。