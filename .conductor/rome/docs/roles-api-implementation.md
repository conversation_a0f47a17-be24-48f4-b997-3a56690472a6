# NocoBase Roles API Implementation

## Overview

Successfully implemented comprehensive roles API support for the NocoBase MCP server, providing full CRUD operations for role management and permission control.

## Implemented Features

### 1. Client API Methods (client.ts)

Added the following methods to `NocoBaseClient` class:

- `listRoles(options?)` - List roles with pagination, filtering, sorting
- `getRole(name, appends?)` - Get specific role by name with optional related data
- `createRole(data)` - Create new role
- `updateRole(name, data)` - Update existing role
- `deleteRole(name)` - Delete role
- `checkRole()` - Check current user's role information
- `setDefaultRole(name)` - Set a role as default for new users

### 2. MCP Tools (roles.ts)

Implemented 7 MCP tools for role management:

#### `list_roles`
- **Purpose**: List all roles with optional filtering, sorting, and pagination
- **Parameters**: 
  - `page` (optional): Page number
  - `pageSize` (optional): Roles per page
  - `filter` (optional): Filter conditions (e.g., `{"hidden": false}`)
  - `sort` (optional): Sort fields (e.g., `["createdAt:desc"]`)
  - `appends` (optional): Related fields to include

#### `get_role`
- **Purpose**: Get detailed information about a specific role
- **Parameters**:
  - `name`: Role name (unique identifier)
  - `appends` (optional): Related fields to include

#### `create_role`
- **Purpose**: Create a new role
- **Parameters**:
  - `name` (optional): Role unique identifier (auto-generated if not provided)
  - `title`: Role display name
  - `description` (optional): Role description
  - `strategy` (optional): Role strategy with allowed actions
  - `default`, `hidden`, `allowConfigure`, `allowNewMenu` (optional): Boolean flags
  - `snippets` (optional): Permission snippets
  - `color` (optional): Role color
  - `roleData` (optional): Additional role data

#### `update_role`
- **Purpose**: Update existing role information
- **Parameters**:
  - `name`: Role name to update
  - All other parameters from create_role (optional)

#### `delete_role`
- **Purpose**: Delete a role
- **Parameters**:
  - `name`: Role name to delete

#### `check_role`
- **Purpose**: Check current user's role information and permissions
- **Parameters**: None

#### `set_default_role`
- **Purpose**: Set a role as the default role for new users
- **Parameters**:
  - `name`: Role name to set as default

### 3. Type Definitions

Added `Role` interface with the following properties:
- `name`: string (unique identifier)
- `title`: string (display name)
- `description`, `color`: string (optional)
- `strategy`: object with actions array (optional)
- `default`, `hidden`, `allowConfigure`, `allowNewMenu`: boolean (optional)
- `snippets`: string array (optional)
- `createdAt`, `updatedAt`: string (optional)
- Additional properties via index signature

## API Endpoints Used

The implementation uses the following NocoBase REST API endpoints:

- `GET /roles:list` - List roles
- `GET /roles:get?filterByTk={name}` - Get role by name
- `POST /roles:create` - Create role
- `POST /roles:update?filterByTk={name}` - Update role
- `POST /roles:destroy?filterByTk={name}` - Delete role
- `GET /roles:check` - Check current role
- `POST /roles:setDefaultRole` - Set default role

## Testing Results

Comprehensive testing was performed with the following results:

✅ **List roles** - Successfully retrieved roles with pagination  
✅ **Get role by name** - Successfully retrieved specific role details  
✅ **Create role** - Successfully created new test role  
✅ **Update role** - Successfully updated role information  
✅ **Delete role** - Successfully deleted test role  
✅ **Check current role** - Successfully retrieved current user's role info  
✅ **Filter roles** - Successfully filtered roles with conditions  

## Usage Examples

### Using MCP Tools

```javascript
// List roles with filtering
await mcpClient.callTool({
  name: 'list_roles',
  arguments: {
    page: 1,
    pageSize: 10,
    filter: { "hidden": false }
  }
});

// Get specific role
await mcpClient.callTool({
  name: 'get_role',
  arguments: { name: 'admin' }
});

// Create role
await mcpClient.callTool({
  name: 'create_role',
  arguments: {
    title: 'Editor Role',
    description: 'Can edit content',
    strategy: {
      actions: ['view', 'update']
    },
    allowConfigure: false
  }
});

// Check current role
await mcpClient.callTool({
  name: 'check_role',
  arguments: {}
});
```

### Using Client API Directly

```javascript
const client = new NocoBaseClient(config);

// List roles
const roles = await client.listRoles({ pageSize: 10 });

// Get role
const role = await client.getRole('admin');

// Create role
const newRole = await client.createRole({
  title: 'Manager Role',
  strategy: { actions: ['view', 'create', 'update'] }
});

// Check current role
const roleInfo = await client.checkRole();
```

## Role Management Features

### Permission Strategy
Roles support a strategy system with configurable actions:
- `create` - Can create new records
- `view` - Can view records
- `update` - Can update records
- `destroy` - Can delete records
- `export` - Can export data
- `importXlsx` - Can import Excel files

### Role Properties
- **Default Role**: Can be set as default for new users
- **Hidden Role**: Can be hidden from UI
- **Configure Permission**: Can configure system settings
- **Menu Permission**: Can create new menus
- **Snippets**: Permission snippets for fine-grained control

### Integration with Users
Roles are linked to users through the `rolesUsers` junction table, supporting:
- Many-to-many relationship between users and roles
- Default role assignment for new users
- Role-based access control throughout the system

## Files Modified/Created

- `src/client.ts` - Added Role interface and client methods
- `src/tools/roles.ts` - Created (new file with MCP tools)
- `src/index.ts` - Added roles tools registration
- `README.md` - Updated feature list
- `scripts/test-roles.js` - Created comprehensive test script

## Integration

The roles API is fully integrated into the MCP server:

1. **Client methods** added to `NocoBaseClient` class
2. **MCP tools** registered in `registerRoleTools()` function
3. **Tool registration** added to main server initialization
4. **Type definitions** added for proper TypeScript support
5. **Documentation** updated to reflect new capabilities

## Next Steps

The roles API implementation is complete and ready for use. Users can now:

1. Manage NocoBase roles through MCP tools
2. Integrate role management into their workflows
3. Build applications that interact with NocoBase role data
4. Perform complex role queries with filtering and pagination
5. Check current user permissions and role information
6. Set up role-based access control systems

All functionality has been tested and verified to work correctly with the NocoBase development environment.
