# NocoBase 集合创建指南

## 问题描述

在创建镇街集合时发现了一个重要问题：**默认字段（id、createdAt、updatedAt、createdBy、updatedBy）没有在字段管理系统中正确定义**。

## 问题根源分析

### 问题现象
1. 使用 `client.createCollection()` 直接创建集合时，只传递了基本配置
2. NocoBase 在数据库层面创建了默认字段（id、createdAt、updatedAt）
3. 但这些字段没有在字段管理系统中创建对应的字段定义
4. 导致 `listFields()` 无法看到这些默认字段，影响管理界面显示

### 对比分析
- **错误方式**：直接调用 `client.createCollection({ name, title, description })`
- **正确方式**：使用 MCP 工具或包含完整字段定义的方式创建

## 解决方案

### 1. 修复现有集合
已创建脚本 `scripts/fix-towns-collection.js` 来为现有的镇街集合添加缺失的字段定义：

```bash
node scripts/fix-towns-collection.js
```

### 2. 更新客户端方法
在 `NocoBaseClient` 中添加了新方法 `createCollectionWithDefaults()`：

```typescript
async createCollectionWithDefaults(params: {
  name: string;
  title?: string;
  description?: string;
  autoGenId?: boolean;
  createdAt?: boolean;
  updatedAt?: boolean;
  createdBy?: boolean;
  updatedBy?: boolean;
  fields?: Partial<Field>[];
}): Promise<Collection>
```

### 3. 正确的集合创建方式

#### 方式一：使用新的客户端方法
```javascript
const collection = await client.createCollectionWithDefaults({
  name: 'towns',
  title: '镇街集合',
  description: '管理镇街信息的集合',
  autoGenId: true,
  createdAt: true,
  updatedAt: true,
  createdBy: true,
  updatedBy: true,
  fields: [
    {
      name: 'city',
      type: 'string',
      interface: 'input',
      uiSchema: {
        type: 'string',
        title: '市',
        'x-component': 'Input',
        required: true
      }
    }
    // ... 其他字段
  ]
});
```

#### 方式二：使用 MCP 工具
通过 MCP 服务器的 `create_collection` 工具创建，该工具已经包含了正确的默认字段逻辑。

## 默认字段定义

### ID 字段
```javascript
{
  name: 'id',
  type: 'bigInt',
  interface: 'id',
  autoIncrement: true,
  primaryKey: true,
  allowNull: false,
  uiSchema: {
    type: 'number',
    title: '{{t("ID")}}',
    'x-component': 'InputNumber',
    'x-read-pretty': true
  }
}
```

### 创建时间字段
```javascript
{
  name: 'createdAt',
  type: 'date',
  interface: 'createdAt',
  field: 'createdAt',
  uiSchema: {
    type: 'datetime',
    title: '{{t("Created at")}}',
    'x-component': 'DatePicker',
    'x-component-props': {},
    'x-read-pretty': true
  }
}
```

### 更新时间字段
```javascript
{
  name: 'updatedAt',
  type: 'date',
  interface: 'updatedAt',
  field: 'updatedAt',
  uiSchema: {
    type: 'datetime',
    title: '{{t("Last updated at")}}',
    'x-component': 'DatePicker',
    'x-component-props': {},
    'x-read-pretty': true
  }
}
```

### 创建人字段
```javascript
{
  name: 'createdBy',
  type: 'belongsTo',
  interface: 'createdBy',
  target: 'users',
  foreignKey: 'createdById',
  targetKey: 'id',
  uiSchema: {
    type: 'object',
    title: '{{t("Created by")}}',
    'x-component': 'AssociationField',
    'x-component-props': {
      fieldNames: {
        value: 'id',
        label: 'nickname'
      }
    },
    'x-read-pretty': true
  }
}
```

### 更新人字段
```javascript
{
  name: 'updatedBy',
  type: 'belongsTo',
  interface: 'updatedBy',
  target: 'users',
  foreignKey: 'updatedById',
  targetKey: 'id',
  uiSchema: {
    type: 'object',
    title: '{{t("Last updated by")}}',
    'x-component': 'AssociationField',
    'x-component-props': {
      fieldNames: {
        value: 'id',
        label: 'nickname'
      }
    },
    'x-read-pretty': true
  }
}
```

## 验证方法

### 检查字段定义
```javascript
const fields = await client.listFields('collection_name');
console.log(`字段数量: ${fields.length}`);
fields.forEach(field => {
  console.log(`${field.name} (${field.type}) - ${field.interface}`);
});
```

### 检查记录数据
```javascript
const records = await client.listRecords('collection_name', { pageSize: 1 });
if (records.data.length > 0) {
  const record = records.data[0];
  Object.keys(record).forEach(key => {
    console.log(`${key}: ${record[key]}`);
  });
}
```

## 最佳实践

1. **始终使用 `createCollectionWithDefaults()` 方法**创建新集合
2. **或者使用 MCP 工具**的 `create_collection` 功能
3. **避免直接使用 `createCollection()`** 除非你明确知道要做什么
4. **创建集合后立即验证**字段定义是否正确
5. **为现有集合补充缺失的字段定义**

## 测试脚本

- `scripts/fix-towns-collection.js` - 修复现有镇街集合
- `tests/test-mcp-create-collection.js` - 测试正确的集合创建方法
- `scripts/compare-collections-fields.js` - 对比不同集合的字段情况
- `scripts/check-towns-fields.js` - 检查镇街集合的字段信息
