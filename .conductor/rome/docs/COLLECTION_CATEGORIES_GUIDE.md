# NocoBase 集合分类管理指南

## 📋 概述

集合分类功能允许您将NocoBase中的集合按照业务逻辑进行分组管理，提高数据组织的清晰度和可维护性。

## 🎯 功能特性

### ✅ 已实现的功能

1. **分类管理**
   - ✅ 列出所有集合分类
   - ✅ 创建新的集合分类
   - ✅ 获取分类详细信息
   - ✅ 更新分类属性
   - ✅ 删除分类
   - ✅ 移动分类排序

2. **集合与分类关联**
   - ✅ 创建集合时指定分类
   - ✅ 查看分类下的所有集合
   - ✅ 支持多分类关联

3. **命名规范验证**
   - ✅ 英文命名建议
   - ✅ 大写字母开头建议
   - ✅ 命名格式检查

## 🛠️ MCP工具使用

### 1. 列出集合分类

```bash
# 基本列表
list_collection_categories

# 包含关联集合信息
list_collection_categories --includeCollections=true

# 分页查询
list_collection_categories --pageSize=10 --page=1
```

### 2. 创建集合分类

```bash
# 基本创建
create_collection_category --name="Administrative" --color="blue"

# 带排序的创建
create_collection_category --name="Geography" --color="green" --sort=2
```

### 3. 获取分类详情

```bash
get_collection_category --id=1
```

### 4. 更新分类

```bash
update_collection_category --id=1 --name="Updated Name" --color="red"
```

### 5. 删除分类

```bash
delete_collection_category --id=1
```

### 6. 移动分类

```bash
move_collection_category --sourceId=1 --targetId=2 --method="insertAfter"
```

## 📚 标准分类体系

系统预设了以下标准分类：

| 分类名称 | 颜色 | 用途说明 | 适用集合示例 |
|---------|------|----------|-------------|
| **Administrative** | 蓝色 | 行政管理相关 | 省市区县、镇街、村居 |
| **Geography** | 绿色 | 地理位置相关 | 地址、坐标、区域 |
| **User Management** | 紫色 | 用户管理相关 | 用户、角色、权限 |
| **Content Management** | 橙色 | 内容管理相关 | 文章、页面、媒体 |
| **Commerce** | 红色 | 商务相关 | 产品、订单、客户 |
| **System** | 灰色 | 系统相关 | 日志、设置、配置 |
| **Workflow** | 黄色 | 工作流相关 | 流程、任务、审批 |
| **Analytics** | 青色 | 分析统计相关 | 报表、指标、统计 |
| **Communication** | 粉色 | 通讯相关 | 消息、通知、邮件 |
| **Organization** | 靛蓝色 | 组织架构相关 | 部门、职位、员工 |

## 🔧 集成使用

### 创建带分类的集合

```bash
create_collection \
  --name="provinces" \
  --title="Provinces" \
  --description="Provincial administrative divisions" \
  --category='["Administrative", "Geography"]'
```

### 使用客户端API

```javascript
// 创建分类
const category = await client.createCollectionCategory({
  name: 'Custom Category',
  color: 'purple',
  sort: 10
});

// 创建带分类的集合
const collection = await client.createCollectionWithDefaults({
  name: 'my_collection',
  title: 'My Collection',
  description: 'A collection with categories',
  category: ['Administrative', 'Custom Category']
});

// 查询分类及其集合
const categories = await client.listCollectionCategories({
  appends: ['collections']
});
```

## 📋 最佳实践

### 1. 分类命名规范

- ✅ 使用英文名称
- ✅ 首字母大写
- ✅ 使用空格分隔单词
- ✅ 保持简洁明了

```bash
# 推荐
"User Management"
"Content Management"
"Administrative"

# 不推荐
"user_management"
"用户管理"
"userManagement"
```

### 2. 分类设计原则

- **单一职责**: 每个分类应该有明确的业务边界
- **互斥性**: 避免分类之间的重叠和混淆
- **扩展性**: 预留未来业务扩展的空间
- **层次性**: 可以通过排序建立分类层次

### 3. 集合分类分配

- **主分类**: 每个集合应该有一个主要分类
- **辅助分类**: 可以添加相关的辅助分类
- **一致性**: 同类型的集合应该使用相同的分类

## 🚀 快速开始

### 1. 初始化标准分类

```bash
node scripts/create-standard-categories.js
```

### 2. 测试分类功能

```bash
node tests/test-collection-categories.js
```

### 3. 创建第一个分类集合

```bash
# 使用MCP工具
create_collection \
  --name="towns" \
  --title="Towns" \
  --description="Town-level administrative divisions" \
  --category='["Administrative", "Geography"]'
```

## 🔍 故障排除

### 常见问题

1. **分类创建失败**
   - 检查分类名称是否重复
   - 确认颜色值是否有效
   - 验证排序值是否为数字

2. **集合分类关联失败**
   - 确认分类ID或名称正确
   - 检查分类是否存在
   - 验证权限设置

3. **分类显示异常**
   - 刷新NocoBase管理界面
   - 检查分类排序设置
   - 确认分类颜色配置

### 调试命令

```bash
# 查看所有分类
list_collection_categories --includeCollections=true

# 检查特定分类
get_collection_category --id=1

# 验证集合关联
# 在NocoBase管理界面中查看集合列表
```

## 📈 未来规划

- [ ] 分类图标支持
- [ ] 分类权限控制
- [ ] 分类模板功能
- [ ] 批量分类操作
- [ ] 分类统计报表

## 🤝 贡献

如果您发现问题或有改进建议，请：

1. 提交Issue描述问题
2. 提供复现步骤
3. 建议解决方案
4. 提交Pull Request

---

**注意**: 删除分类时，关联的集合不会被删除，只是会失去分类关联。建议在删除分类前先检查其关联的集合。
