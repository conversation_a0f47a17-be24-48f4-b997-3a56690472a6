# NocoBase Menu Block 操作与 API 参数对应关系

本文档详细描述了 NocoBase 中 Menu Block 的各种操作与其对应的 API 调用参数，为开发者提供完整的参考指南。

## 目录

1. [Menu Block 架构概述](#1-menu-block-架构概述)
2. [菜单路由管理操作](#2-菜单路由管理操作)
3. [菜单项创建操作](#3-菜单项创建操作)
4. [菜单项更新操作](#4-菜单项更新操作)
5. [菜单项删除操作](#5-菜单项删除操作)
6. [菜单项移动操作](#6-菜单项移动操作)
7. [菜单权限管理操作](#7-菜单权限管理操作)
8. [菜单区块配置](#8-菜单区块配置)
9. [移动端菜单操作](#9-移动端菜单操作)
10. [参数传递机制](#10-参数传递机制)

---

## 1. Menu Block 架构概述

### 核心组件结构

```
Menu Block (菜单区块容器)
├── Desktop Menu (桌面端菜单)
│   ├── Menu Designer (菜单设计器)
│   ├── Menu.Item (菜单项)
│   ├── Menu.SubMenu (子菜单)
│   ├── Menu.URL (链接菜单项)
│   └── Menu Action Initializers (菜单操作初始化器)
├── Mobile Menu (移动端菜单)
│   ├── MMenu (移动端菜单容器)
│   ├── MMenu.Item (移动端菜单项)
│   ├── MMenu.Designer (移动端菜单设计器)
│   └── Mobile Menu Actions (移动端菜单操作)
└── Menu Data Management (菜单数据管理)
    ├── desktopRoutes Collection (菜单路由集合)
    ├── Roles Management (角色权限管理)
    └── ACL Control (访问控制)
```

### 关键文件位置

- **Desktop Menu**: `/packages/core/client/src/schema-component/antd/menu/Menu.tsx`
- **Menu Designer**: `/packages/core/client/src/schema-component/antd/menu/Menu.Designer.tsx`
- **Mobile Menu**: `/packages/plugins/@nocobase/plugin-mobile-client/src/client/core/schema/components/menu/Menu.tsx`
- **Server Routes**: `/packages/plugins/@nocobase/plugin-client/src/server/collections/desktopRoutes.ts`
- **Server Actions**: `/packages/plugins/@nocobase/plugin-client/src/server/server.ts`

### 数据库结构

**Collection**: `desktopRoutes`

**主要字段**:
- `id`: 主键，自增整数
- `title`: 菜单标题
- `tooltip`: 提示信息
- `icon`: 图标
- `type`: 菜单类型 (group, page, link, url)
- `schemaUid`: 页面 schema UID
- `menuSchemaUid`: 菜单 schema UID
- `parentId`: 父菜单 ID
- `sort`: 排序权重
- `options`: JSON 配置选项
- `hideInMenu`: 是否在菜单中隐藏
- `enableTabs`: 是否启用标签页
- `enableHeader`: 是否启用头部
- `hidden`: 是否隐藏
- `roles`: 关联角色 (多对多关系)

---

## 2. 菜单路由管理操作

### Create Route (创建路由)

- **API 方法**: `POST /api/desktopRoutes:create`
- **操作类型**: 创建新的菜单路由项
- **API 参数**:
  ```typescript
  {
    title: string,                           // 菜单标题
    tooltip?: string,                       // 提示信息
    icon?: string,                          // 图标
    type: 'group' | 'page' | 'link' | 'url', // 菜单类型
    schemaUid?: string,                     // 页面 schema UID
    menuSchemaUid?: string,                 // 菜单 schema UID
    tabSchemaName?: string,                 // 标签页 schema 名称
    parentId?: number | null,               // 父菜单 ID
    options?: object,                       // JSON 配置选项
    sort?: number,                          // 排序权重
    hideInMenu?: boolean,                   // 是否在菜单中隐藏
    enableTabs?: boolean,                   // 是否启用标签页
    enableHeader?: boolean,                 // 是否启用头部
    hidden?: boolean,                       // 是否隐藏
  }
  ```

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.tsx:234`

**实现逻辑**:
```typescript
const createRoute = useCallback(
  async (values: NocoBaseDesktopRoute, refreshAfterCreate = true) => {
    const res = await resource.create({
      values,
    });
    refreshAfterCreate && refreshRoutes();
    return res;
  },
  [resource, refreshRoutes],
);
```

### Update Route (更新路由)

- **API 方法**: `PUT /api/desktopRoutes:update`
- **操作类型**: 更新现有菜单路由项
- **API 参数**:
  ```typescript
  {
    filterByTk: number | string,            // 路由 ID
    values: {
      title?: string,                       // 菜单标题
      tooltip?: string,                     // 提示信息
      icon?: string,                        // 图标
      options?: object,                     // JSON 配置选项
      hideInMenu?: boolean,                 // 是否在菜单中隐藏
      enableTabs?: boolean,                 // 是否启用标签页
      enableHeader?: boolean,               // 是否启用头部
      hidden?: boolean,                     // 是否隐藏
      // ... 其他可更新字段
    }
  }
  ```

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.tsx:245`

**实现逻辑**:
```typescript
const updateRoute = useCallback(
  async (filterByTk: any, values: NocoBaseDesktopRoute, refreshAfterUpdate = true) => {
    const res = await resource.update({
      filterByTk,
      values,
    });
    refreshAfterUpdate && refreshRoutes();
    return res;
  },
  [resource, refreshRoutes],
);
```

### Delete Route (删除路由)

- **API 方法**: `DELETE /api/desktopRoutes:destroy`
- **操作类型**: 删除菜单路由项
- **API 参数**:
  ```typescript
  {
    filterByTk: number | string,            // 路由 ID
  }
  ```

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.tsx:257`

**实现逻辑**:
```typescript
const deleteRoute = useCallback(
  async (filterByTk: any, refreshAfterDelete = true) => {
    const res = await resource.destroy({
      filterByTk,
    });
    refreshAfterDelete && refreshRoutes();
    return res;
  },
  [refreshRoutes, resource],
);
```

### Move Route (移动路由)

- **API 方法**: `POST /api/desktopRoutes:move`
- **操作类型**: 移动菜单路由项到新位置
- **API 参数**:
  ```typescript
  {
    sourceId: string | number,               // 源路由 ID
    targetId?: string | number,             // 目标路由 ID
    targetScope?: any,                      // 目标范围
    sortField?: string,                     // 排序字段
    sticky?: boolean,                       // 是否固定
    method?: 'insertAfter' | 'prepend',     // 插入方式
  }
  ```

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.tsx:268`

**实现逻辑**:
```typescript
const moveRoute = useCallback(
  async ({
    sourceId,
    targetId,
    targetScope,
    sortField,
    sticky,
    method,
    refreshAfterMove = true,
  }: {
    sourceId: string | number;
    targetId?: string | number;
    targetScope?: any;
    sortField?: string;
    sticky?: boolean;
    method?: 'insertAfter' | 'prepend';
    refreshAfterMove?: boolean;
  }) => {
    const res = await resource.move({ sourceId, targetId, targetScope, sortField, sticky, method });
    refreshAfterMove && refreshRoutes();
    return res;
  },
  [refreshRoutes, resource],
);
```

---

## 3. 菜单项创建操作

### Create Group Menu Item (创建分组菜单项)

- **操作类型**: 创建菜单分组
- **API 调用顺序**:
  1. 调用 `desktopRoutes:create` 创建路由
  2. 调用 `desktopRoutes:move` 移动到目标位置

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.Designer.tsx:117`

**实现逻辑**:
```typescript
const { data } = await createRoute({
  type: NocoBaseDesktopRouteType.group,
  title,
  icon,
  parentId: insertPosition === 'beforeEnd' ? route?.id : parentRoute?.id,
  schemaUid,
});

if (insertPositionToMethod[insertPosition]) {
  await moveRoute({
    sourceId: data?.data?.id,
    targetId: route?.id,
    sortField: 'sort',
    method: insertPositionToMethod[insertPosition],
  });
}
```

### Create Page Menu Item (创建页面菜单项)

- **操作类型**: 创建页面菜单项
- **API 调用顺序**:
  1. 创建页面 schema
  2. 调用 `desktopRoutes:create` 创建路由
  3. 调用 `desktopRoutes:move` 移动到目标位置

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.Designer.tsx:170`

**实现逻辑**:
```typescript
const { data } = await createRoute({
  type: NocoBaseDesktopRouteType.page,
  title,
  icon,
  schemaUid: pageSchemaUid,
  parentId: insertPosition === 'beforeEnd' ? route?.id : parentRoute?.id,
});

if (insertPositionToMethod[insertPosition]) {
  await moveRoute({
    sourceId: data?.data?.id,
    targetId: route?.id,
    sortField: 'sort',
    method: insertPositionToMethod[insertPosition],
  });
}
```

### Create Link Menu Item (创建链接菜单项)

- **操作类型**: 创建链接菜单项
- **API 调用顺序**:
  1. 调用 `desktopRoutes:create` 创建路由
  2. 调用 `desktopRoutes:move` 移动到目标位置

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.Designer.tsx:233`

**实现逻辑**:
```typescript
const { data } = await createRoute({
  type: NocoBaseDesktopRouteType.link,
  title,
  icon,
  options: {
    href,
    target,
  },
  parentId: insertPosition === 'beforeEnd' ? route?.id : parentRoute?.id,
});

if (insertPositionToMethod[insertPosition]) {
  await moveRoute({
    sourceId: data?.data?.id,
    targetId: route?.id,
    sortField: 'sort',
    method: insertPositionToMethod[insertPosition],
  });
}
```

---

## 4. 菜单项更新操作

### Update Menu Item Properties (更新菜单项属性)

- **操作类型**: 更新菜单项的基本属性
- **API 调用**: `desktopRoutes:update`

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.Designer.tsx:384`

**实现逻辑**:
```typescript
updateRoute(fieldSchema['__route__'].id, {
  title: values.title,
  icon: values.icon,
  tooltip: values.tooltip,
  hideInMenu: values.hideInMenu,
});
```

### Update Menu Item Schema (更新菜单项 Schema)

- **操作类型**: 更新菜单项的 schema 配置
- **API 调用**: `desktopRoutes:update`

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.Designer.tsx:403`

**实现逻辑**:
```typescript
updateRoute(fieldSchema['__route__'].id, {
  options: {
    ...fieldSchema['__route__']?.options,
    href: values.href,
    target: values.target,
  },
});
```

---

## 5. 菜单项删除操作

### Delete Menu Item (删除菜单项)

- **操作类型**: 删除菜单项及其子项
- **API 调用**: `desktopRoutes:destroy`

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.Designer.tsx:490`

**实现逻辑**:
```typescript
fieldSchema['__route__']?.id && deleteRoute(fieldSchema['__route__'].id);
```

**注意**: 删除操作会级联删除所有子菜单项（通过数据库的 `ON DELETE CASCADE` 约束）。

---

## 6. 菜单项移动操作

### Move Menu Item (移动菜单项)

- **操作类型**: 拖拽移动菜单项
- **API 调用**: `desktopRoutes:move`

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.Designer.tsx:472`

**实现逻辑**:
```typescript
await moveRoute({
  sourceId: dragSourceNode.id,
  targetId: dropTargetNode.id,
  method: dropPosition === 'inside' ? 'append' : 'insertBefore',
});
```

### Reorder Menu Items (重新排序菜单项)

- **操作类型**: 重新排序同级菜单项
- **API 调用**: `desktopRoutes:move`

**实现逻辑**:
```typescript
await moveRoute({
  sourceId: dragSourceNode.id,
  targetId: dropTargetNode.id,
  method: dropPosition === 'inside' ? 'append' : 'insertBefore',
});
```

---

## 7. 菜单权限管理操作

### List Accessible Routes (获取可访问路由)

- **API 方法**: `GET /api/desktopRoutes:listAccessible`
- **操作类型**: 获取当前用户可访问的菜单路由
- **API 参数**:
  ```typescript
  {
    tree?: boolean,                        // 是否返回树形结构
    filter?: object,                      // 筛选条件
    sort?: string,                        // 排序条件
    page?: number,                        // 页码
    pageSize?: number,                    // 每页条数
  }
  ```

**源码位置**: `/packages/plugins/@nocobase/plugin-client/src/server/server.ts:251`

**实现逻辑**:
```typescript
this.app.resourceManager.registerActionHandler('desktopRoutes:listAccessible', async (ctx, next) => {
  const desktopRoutesRepository = ctx.db.getRepository('desktopRoutes');
  const rolesRepository = ctx.db.getRepository('roles');

  if (ctx.state.currentRoles.includes('root')) {
    ctx.body = await desktopRoutesRepository.find({
      tree: true,
      ...ctx.query,
    });
    return await next();
  }

  const roles = await rolesRepository.find({
    filterByTk: ctx.state.currentRoles,
    appends: ['desktopRoutes'],
  });

  const desktopRoutesId = roles.flatMap((x) => x.get('desktopRoutes')).map((item) => item.id);

  if (desktopRoutesId) {
    const ids = (await Promise.all(desktopRoutesId)).flat();
    const result = await desktopRoutesRepository.find({
      tree: true,
      ...ctx.query,
      filter: {
        id: ids,
      },
    });

    ctx.body = result;
  }

  await next();
});
```

### Set Role Menu Permissions (设置角色菜单权限)

- **API 方法**: `POST /api/roles.desktopRoutes:set`
- **操作类型**: 设置角色的菜单访问权限
- **API 参数**:
  ```typescript
  {
    resourceName: string,                  // 资源名称
    sourceId: string | number,            // 角色 ID
    values: (string | number)[]           // 菜单路由 ID 数组
  }
  ```

**源码位置**: `/packages/plugins/@nocobase/plugin-client/src/server/server.ts:286`

**实现逻辑**:
```typescript
this.app.resourceManager.registerActionHandler('roles.desktopRoutes:set', async (ctx, next) => {
  let { values } = ctx.action.params;
  if (values.length) {
    const instances = await this.app.db.getRepository('desktopRoutes').find({
      filter: {
        $or: [{ id: { $in: values } }, { parentId: { $in: values } }],
      },
    });
    values = instances.map((instance) => instance.get('id'));
  }
  const { resourceName, sourceId } = ctx.action;
  const repository = this.app.db.getRepository<MultipleRelationRepository>(resourceName, sourceId);
  await repository['set'](values);

  ctx.status = 200;
  await next();
});
```

---

## 8. 菜单区块配置

### Desktop Menu Block Configuration (桌面端菜单区块配置)

```typescript
{
  type: 'void',
  'x-component': 'Menu',
  'x-designer': 'Menu.Designer',
  'x-initializer': 'MenuItemInitializers',
  'x-component-props': {
    mode: 'mix',                          // 菜单模式: 'horizontal' | 'vertical' | 'mix'
    theme: 'dark',                       // 主题: 'light' | 'dark'
    defaultSelectedUid: 'u8',             // 默认选中项 UID
    onSelect: '{{ onSelect }}',           // 选择回调
    sideMenuRefScopeKey: 'sideMenuRef',   // 侧边菜单引用键
  },
  properties: {},                        // 菜单项属性
}
```

### Mobile Menu Block Configuration (移动端菜单区块配置)

```typescript
{
  type: 'void',
  'x-component': 'MMenu',
  'x-designer': 'MMenu.Designer',
  'x-component-props': {},
  properties: {
    // 菜单项配置
    menuItem1: {
      type: 'void',
      title: '菜单项1',
      'x-component': 'MMenu.Item',
      'x-component-props': {
        name: '菜单项1',
        icon: 'HomeOutlined',
      },
      'x-designer': 'MMenu.Item.Designer',
      properties: {
        page: PageSchema,                 // 页面配置
      },
    },
  },
}
```

### Menu Item Configuration (菜单项配置)

```typescript
{
  type: 'void',
  title: '用户管理',
  'x-component': 'Menu.Item',
  'x-designer': 'Menu.Item.Designer',
  'x-component-props': {
    icon: 'UserOutlined',
  },
  properties: {
    page: {
      type: 'void',
      'x-component': 'Page',
      properties: {
        // 页面内容配置
      },
    },
  },
}
```

---

## 9. 移动端菜单操作

### Add Mobile Menu Item (添加移动端菜单项)

- **操作类型**: 添加移动端菜单项
- **实现方式**: 通过 Schema 初始化器

**源码位置**: `/packages/plugins/@nocobase/plugin-mobile-client/src/client/core/schema/components/menu/Menu.tsx:37`

**实现逻辑**:
```typescript
const onAddMenuItem = (values: any) => {
  const properties = {
    page: PageSchema,
  };

  return insertBeforeEnd({
    type: 'void',
    title: values.name,
    'x-component': 'MMenu.Item',
    'x-component-props': values,
    'x-designer': 'MMenu.Item.Designer',
    properties,
  });
};
```

### Mobile Menu Navigation (移动端菜单导航)

- **操作类型**: 移动端菜单导航
- **实现方式**: 通过 React Router

**源码位置**: `/packages/plugins/@nocobase/plugin-mobile-client/src/client/core/schema/components/menu/Menu.Item.tsx:43`

**实现逻辑**:
```typescript
const onToPage = () => {
  const locationPath = location.pathname.endsWith('/') ? location.pathname.slice(0, -1) : location.pathname;
  navigate(params.name ? `/mobile/${fieldSchema['x-uid']}` : `${locationPath}/${fieldSchema['x-uid']}`);
};
```

---

## 10. 参数传递机制

### Menu Routes Hook (菜单路由钩子)

**源码位置**: `/packages/core/client/src/schema-component/antd/menu/Menu.tsx:229`

**实现逻辑**:
```typescript
export const useNocoBaseRoutes = (collectionName = 'desktopRoutes') => {
  const api = useAPIClient();
  const resource = useMemo(() => api.resource(collectionName), [api, collectionName]);
  const { refresh: refreshRoutes } = useAllAccessDesktopRoutes();

  const createRoute = useCallback(
    async (values: NocoBaseDesktopRoute, refreshAfterCreate = true) => {
      const res = await resource.create({
        values,
      });
      refreshAfterCreate && refreshRoutes();
      return res;
    },
    [resource, refreshRoutes],
  );

  const updateRoute = useCallback(
    async (filterByTk: any, values: NocoBaseDesktopRoute, refreshAfterUpdate = true) => {
      const res = await resource.update({
        filterByTk,
        values,
      });
      refreshAfterUpdate && refreshRoutes();
      return res;
    },
    [resource, refreshRoutes],
  );

  const deleteRoute = useCallback(
    async (filterByTk: any, refreshAfterDelete = true) => {
      const res = await resource.destroy({
        filterByTk,
      });
      refreshAfterDelete && refreshRoutes();
      return res;
    },
    [refreshRoutes, resource],
  );

  const moveRoute = useCallback(
    async ({
      sourceId,
      targetId,
      targetScope,
      sortField,
      sticky,
      method,
      refreshAfterMove = true,
    }: {
      sourceId: string | number;
      targetId?: string | number;
      targetScope?: any;
      sortField?: string;
      sticky?: boolean;
      method?: 'insertAfter' | 'prepend';
      refreshAfterMove?: boolean;
    }) => {
      const res = await resource.move({ sourceId, targetId, targetScope, sortField, sticky, method });
      refreshAfterMove && refreshRoutes();
      return res;
    },
    [refreshRoutes, resource],
  );

  return { createRoute, updateRoute, deleteRoute, moveRoute };
};
```

### Menu Data Flow Example (菜单数据流示例)

```typescript
// 1. 创建菜单分组
const { data } = await createRoute({
  type: 'group',
  title: '系统管理',
  icon: 'SettingOutlined',
  parentId: null,
});

// 2. 创建页面菜单项
const { data: pageData } = await createRoute({
  type: 'page',
  title: '用户管理',
  icon: 'UserOutlined',
  schemaUid: 'user-management-page',
  parentId: data.data.id,
});

// 3. 移动菜单项到指定位置
await moveRoute({
  sourceId: pageData.data.id,
  targetId: targetMenuId,
  method: 'insertAfter',
});

// 4. 更新菜单项属性
await updateRoute(pageData.data.id, {
  title: '用户管理（新）',
  hideInMenu: false,
  enableTabs: true,
});

// 5. 获取可访问菜单
const accessibleRoutes = await api.request({
  url: 'desktopRoutes:listAccessible',
  method: 'GET',
  params: {
    tree: true,
  },
});

// 6. 设置角色菜单权限
await api.request({
  url: 'roles.desktopRoutes:set',
  method: 'POST',
  data: {
    resourceName: 'desktopRoutes',
    sourceId: roleId,
    values: [routeId1, routeId2, routeId3],
  },
});
```

---

## 总结

本文档详细描述了 NocoBase Menu Block 的完整操作体系，包括：

1. **菜单路由管理**: 创建、更新、删除、移动菜单路由
2. **菜单项操作**: 分组、页面、链接等不同类型菜单项的创建
3. **权限管理**: 基于角色的菜单访问权限控制
4. **菜单配置**: 桌面端和移动端菜单的区块配置
5. **数据流**: 从前端到后端的完整参数传递机制

所有 API 操作都基于 `desktopRoutes` 集合，支持树形结构、权限控制、多语言等高级功能。开发者可以基于这份文档准确理解和使用 NocoBase 的 Menu Block 功能。

---

**文档版本**: v1.0  
**更新日期**: 2024-08-09  
**NocoBase 版本**: 基于最新源码分析