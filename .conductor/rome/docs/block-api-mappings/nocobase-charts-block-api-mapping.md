# NocoBase Charts Block 操作与 API 参数对应关系

本文档详细描述了 NocoBase 中 Charts Block 的各种操作与其对应的 API 调用参数，为开发者提供完整的参考指南。

## 目录

1. [Charts Block 架构概述](#1-charts-block-架构概述)
2. [图表数据查询操作](#2-图表数据查询操作)
3. [图表刷新操作](#3-图表刷新操作)
4. [图表自动刷新操作](#4-图表自动刷新操作)
5. [图表配置操作](#5-图表配置操作)
6. [图表数据处理机制](#6-图表数据处理机制)
7. [图表区块配置](#7-图表区块配置)
8. [前端组件配置示例](#8-前端组件配置示例)
9. [参数传递机制](#9-参数传递机制)

---

## 1. Charts Block 架构概述

### 核心组件结构

```
ChartV2Block (图表区块容器)
├── ChartDataProvider (图表数据提供者)
│   └── ChartDataContext (图表数据上下文)
├── ChartFilterProvider (图表筛选提供者)
│   └── ChartFilterContext (筛选上下文)
├── ChartConfigProvider (图表配置提供者)
│   └── ChartConfigContext (配置上下文)
├── ChartRendererProvider (图表渲染提供者)
│   ├── ChartRendererContext (渲染上下文)
│   ├── ChartRenderer (图表渲染器)
│   │   ├── Chart Components (图表组件)
│   │   │   ├── G2Plot Charts (G2Plot图表)
│   │   │   ├── Ant Design Charts (AntD图表)
│   │   │   └── Custom Charts (自定义图表)
│   │   └── RefreshButton (刷新按钮)
│   └── AutoRefresh (自动刷新)
└── GlobalAutoRefreshProvider (全局自动刷新)
```

### 关键文件位置

- **ChartBlock**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/block/ChartBlock.tsx`
- **ChartRenderer**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/renderer/ChartRenderer.tsx`
- **ChartRendererProvider**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/renderer/ChartRendererProvider.tsx`
- **Chart Actions**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/initializers/chartActions.tsx`
- **Chart Block Actions**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/initializers/chartBlockActions.tsx`
- **Server Query**: `/packages/plugins/@nocobase/plugin-data-visualization/src/server/actions/query.ts`

---

## 2. 图表数据查询操作

### Chart Data Query (图表数据查询)

- **x-action**: `charts:query`
- **API 方法**: `POST`
- **API 参数**:
  ```typescript
  {
    uid: string,                              // 图表唯一标识符
    dataSource: string,                       // 数据源名称
    collection: string,                       // 集合名称
    measures: MeasureProps[],                 // 度量字段
    dimensions: DimensionProps[],             // 维度字段
    orders?: OrderProps[],                    // 排序字段
    filter?: object,                          // 筛选条件
    limit?: number,                           // 限制条数
    offset?: number,                          // 偏移量
    sql?: {
      fields?: string,                        // SQL字段
      clauses?: string,                       // SQL子句
    },
    cache?: {
      enabled: boolean,                       // 是否启用缓存
      ttl: number,                            // 缓存时间(秒)
    },
    refresh?: boolean                         // 是否刷新缓存
  }
  ```

**类型定义**:
```typescript
interface MeasureProps {
  field: string | string[];                   // 字段路径
  aggregation?: string;                      // 聚合函数
  alias?: string;                            // 别名
}

interface DimensionProps {
  field: string | string[];                  // 字段路径
  alias?: string;                            // 别名
  format?: string;                           // 格式化
}

interface OrderProps {
  field: string;                             // 字段名
  order: 'asc' | 'desc';                     // 排序方向
}
```

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/renderer/ChartRendererProvider.tsx:111`

**实现逻辑**:
```typescript
const res = await api.request({
  url: 'charts:query',
  method: 'POST',
  data: {
    uid: schema?.['x-uid'],
    dataSource,
    collection,
    ...queryWithFilter,
    filter: removeUnparsableFilter(queryWithFilter.filter),
    dimensions: (query?.dimensions || []).map((item: DimensionProps) => {
      const dimension = { ...item };
      if (item.format && !item.alias) {
        const { alias } = parseField(item.field);
        dimension.alias = alias;
      }
      return dimension;
    }),
    measures: (query?.measures || []).map((item: MeasureProps) => {
      const measure = { ...item };
      if (item.aggregation && !item.alias) {
        const { alias } = parseField(item.field);
        measure.alias = alias;
      }
      return measure;
    }),
  },
});
```

**服务端处理**: `/packages/plugins/@nocobase/plugin-data-visualization/src/server/actions/query.ts:220`

---

## 3. 图表刷新操作

### Refresh Chart (刷新图表)

- **x-action**: `refresh`
- **x-use-component-props**: `useChartRefreshActionProps`
- **API 参数**: 无需额外参数，直接调用 `service.refresh()`

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/initializers/RefreshAction.tsx:78`

**实现逻辑**:
```typescript
export const useChartRefreshActionProps = () => {
  const { service } = useContext(ChartRendererContext);
  return {
    onClick: service.refresh,
  };
};
```

### Refresh Chart Block (刷新图表区块)

- **x-action**: `refresh`
- **x-use-component-props**: `useChartBlockRefreshActionProps`
- **API 参数**: 无需额外参数，调用 `refreshCharts()`

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/initializers/BlockRefreshAction.tsx:78`

**实现逻辑**:
```typescript
export const useChartBlockRefreshActionProps = () => {
  const { refreshCharts } = useContext(GlobalAutoRefreshContext);
  return {
    onClick: () => {
      refreshCharts?.();
    },
  };
};
```

---

## 4. 图表自动刷新操作

### Auto Refresh Configuration (自动刷新配置)

- **x-component-props**: `autoRefresh`
- **API 参数**: 
  ```typescript
  {
    autoRefresh: number | boolean    // 刷新间隔(秒)或false关闭
  }
  ```

**支持的刷新间隔**:
```typescript
const interval = {
  5: '5s',      // 5秒
  10: '10s',    // 10秒
  30: '30s',    // 30秒
  60: '1m',     // 1分钟
  300: '5m',    // 5分钟
  900: '15m',   // 15分钟
  1800: '30m',  // 30分钟
  3600: '1h',   // 1小时
  7200: '2h',   // 2小时
  86400: '1d',  // 1天
};
```

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/renderer/ChartRendererProvider.tsx:168`

**实现逻辑**:
```typescript
useEffect(() => {
  if (disableAutoRefresh) {
    return;
  }
  if (!autoRefresh) {
    addGlobalAutoRefreshChart?.(schema?.['x-uid'], { service });
    return;
  }
  removeGlobalAutoRefreshChart?.(schema?.['x-uid']);
  const refresh = autoRefresh as number;
  const timer = setInterval(service.refresh, refresh * 1000);
  return () => {
    clearInterval(timer);
  };
}, [autoRefresh, disableAutoRefresh]);
```

---

## 5. 图表配置操作

### Chart Configuration (图表配置)

- **操作类型**: 表单配置保存
- **API 参数**:
  ```typescript
  {
    query: QueryProps,           // 查询配置
    config: {
      chartType: string,        // 图表类型
      general: any,             // 通用配置
      advanced: any,            // 高级配置
      title?: string,           // 标题
      bordered?: boolean,        // 边框
    },
    transform?: TransformProps[], // 数据转换配置
    mode?: 'builder' | 'sql',   // 模式
  }
  ```

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/configure/ChartConfigure.tsx:186`

**实现逻辑**:
```typescript
onOk={() => {
  const { query, config, transform, mode } = form.values;
  const { title, bordered } = config || {};
  const rendererProps = {
    query,
    config,
    dataSource,
    collection,
    transform,
    mode: mode || 'builder',
  };
  // 更新schema和field
  schema['x-component-props'] = {
    ...schema['x-component-props'],
    title,
    bordered,
  };
  schema['x-decorator-props'] = rendererProps;
  field.decoratorProps = rendererProps;
  field.componentProps = {
    ...field.componentProps,
    title,
    bordered,
  };
  dn.emit('patch', { schema });
}}
```

### Run Query (运行查询)

- **操作类型**: 手动触发查询
- **API 参数**: 使用当前表单的查询配置

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/configure/ChartConfigure.tsx:160`

**实现逻辑**:
```typescript
onClick={async () => {
  const queryField = form.query('query').take() as ObjectField;
  try {
    await queryField?.validate();
  } catch (e) {
    return;
  }
  try {
    await service.runAsync(dataSource, collection, form.values.query, true);
  } catch (e) {
    console.log(e);
  }
  queryReact(form, initChart);
}}
```

---

## 6. 图表数据处理机制

### 数据查询流程

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/server/actions/query.ts:220`

```typescript
export const query = async (ctx: Context, next: Next) => {
  const { dataSource } = ctx.action.params.values as QueryParams;
  const db = getDB(ctx, dataSource) || ctx.db;
  const queryParser = createQueryParser(db);
  try {
    await compose([
      checkPermission,        // 权限检查
      cacheMiddleware,        // 缓存处理
      parseVariables,         // 变量解析
      parseFieldAndAssociations, // 字段和关联解析
      queryParser.parse(),    // 查询解析
      queryData,              // 数据查询
      postProcess,            // 后处理
    ])(ctx, next);
  } catch (err) {
    ctx.throw(500, err);
  }
};
```

### 字段解析机制

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/server/actions/query.ts:86`

```typescript
const parseField = (selected: { field: string | string[]; alias?: string }) => {
  let target: string;
  let name: string;
  if (!Array.isArray(selected.field)) {
    name = selected.field;
  } else if (selected.field.length === 1) {
    name = selected.field[0];
  } else if (selected.field.length > 1) {
    [target, name] = selected.field;
  }
  
  const rawAttributes = collection.model.getAttributes();
  let field = rawAttributes[name]?.field || name;
  let fieldType = fields.get(name)?.type;
  
  if (target) {
    const targetField = fields.get(target) as Field;
    const targetCollection = db.getCollection(targetField.target);
    const targetFields = targetCollection.fields;
    fieldType = targetFields.get(name)?.type;
    field = `${target}.${field}`;
    name = `${target}.${name}`;
  } else {
    field = `${collectionName}.${field}`;
  }
  
  return {
    ...selected,
    field,
    name,
    type: fieldType,
    alias: selected.alias || name,
  };
};
```

### 数据转换机制

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/renderer/ChartRenderer.tsx:68`

```typescript
const chartProps = chart.getProps({
  data,
  general,
  advanced,
  fieldProps: Object.keys(data[0] || {}).reduce((props, name) => {
    if (!props[name]) {
      const field = getField(fields, name.split('.'));
      const transformer = transformers[name];
      props[name] = { 
        label: field?.label || name, 
        transformer, 
        interface: field?.interface 
      };
    }
    return props;
  }, {}),
});
```

---

## 7. 图表区块配置

### 基础图表区块配置

```typescript
{
  type: 'void',
  'x-component': 'CardItem',
  'x-decorator': 'ChartV2Block',
  'x-settings': 'chartBlockSettings:chartBlock',
  'x-toolbar': 'BlockSchemaToolbar',
  'x-decorator-props': {
    dataSource: 'main',
    collection: 'users',
    query: {
      measures: [
        {
          field: 'id',
          aggregation: 'count',
          alias: 'user_count'
        }
      ],
      dimensions: [
        {
          field: 'status',
          alias: 'user_status'
        }
      ],
      filter: { created_at: { $gt: '2024-01-01' } },
      limit: 100
    },
    config: {
      chartType: 'bar',
      general: {
        title: '用户统计图表',
        bordered: true
      },
      advanced: {}
    },
    transform: [
      {
        field: 'user_count',
        type: 'number',
        format: '0,0'
      }
    ]
  },
  properties: {
    actions: {
      type: 'void',
      'x-component': 'ActionBar',
      'x-initializer': 'chartBlock:configureActions',
      'x-component-props': {
        style: {
          marginBottom: 'var(--nb-spacing)',
        },
      },
    },
    chart: {
      type: 'void',
      'x-component': 'ChartRenderer',
      'x-use-component-props': 'useChartRendererProps',
    },
  },
}
```

### SQL 模式图表配置

```typescript
{
  'x-decorator-props': {
    dataSource: 'main',
    collection: 'users',
    mode: 'sql',
    query: {
      sql: {
        fields: 'status, COUNT(*) as count',
        clauses: 'GROUP BY status'
      }
    },
    config: {
      chartType: 'pie',
      general: {
        title: '用户状态分布'
      }
    }
  }
}
```

### 带缓存的图表配置

```typescript
{
  'x-decorator-props': {
    dataSource: 'main',
    collection: 'orders',
    query: {
      measures: [
        {
          field: 'amount',
          aggregation: 'sum',
          alias: 'total_amount'
        }
      ],
      dimensions: [
        {
          field: 'created_at',
          format: 'YYYY-MM'
        }
      ],
      cache: {
        enabled: true,
        ttl: 300  // 5分钟缓存
      }
    }
  }
}
```

---

## 8. 前端组件配置示例

### 图表操作按钮配置

```typescript
{
  type: 'void',
  'x-component': 'ActionBar',
  'x-initializer': 'chart:configureActions',
  properties: {
    refresh: {
      type: 'void',
      title: '刷新',
      'x-action': 'refresh',
      'x-component': 'Action',
      'x-use-component-props': 'useChartRefreshActionProps',
      'x-component-props': {
        size: 'small',
        component: 'RefreshButton',
        autoRefresh: 60,  // 60秒自动刷新
      },
      'x-settings': 'chartActionSettings:refresh',
    },
  },
}
```

### 图表区块操作按钮配置

```typescript
{
  type: 'void',
  'x-component': 'ActionBar',
  'x-initializer': 'chartBlock:configureActions',
  properties: {
    refresh: {
      type: 'void',
      title: '刷新',
      'x-action': 'refresh',
      'x-component': 'Action',
      'x-use-component-props': 'useChartBlockRefreshActionProps',
      'x-component-props': {
        component: 'BlockRefreshButton',
      },
      'x-settings': 'chartBlockActionSettings:refresh',
    },
  },
}
```

### 图表渲染器配置

```typescript
{
  type: 'void',
  'x-component': 'ChartRenderer',
  'x-use-component-props': 'useChartRendererProps',
  'x-component-props': {
    title: '销售趋势图',
    bordered: true,
  },
  'x-decorator': 'ChartRendererProvider',
  'x-decorator-props': {
    dataSource: 'main',
    collection: 'orders',
    query: {
      measures: [
        {
          field: 'total',
          aggregation: 'sum',
          alias: 'sales_total'
        }
      ],
      dimensions: [
        {
          field: 'order_date',
          format: 'YYYY-MM-DD'
        }
      ],
      orders: [
        {
          field: 'order_date',
          order: 'asc'
        }
      ],
      filter: {
        status: 'completed'
      }
    },
    config: {
      chartType: 'line',
      general: {
        title: '每日销售趋势',
        showLegend: true,
        showDataLabel: false
      },
      advanced: {
        smooth: true,
        area: true
      }
    }
  }
}
```

---

## 9. 参数传递机制

### 图表查询参数获取流程

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/client/renderer/ChartRendererProvider.tsx:98`

```typescript
const service = useRequest(
  async (dataSource, collection, query, manual) => {
    if (!(collection && query?.measures?.length)) return;
    if (enabled && !form) return;
    
    const filterValues = getFilter();
    const parsedFilter = await parseFilter(query.filter);
    const parsedQuery = { ...query, filter: parsedFilter };
    const config = { dataSource, collection, query: parsedQuery };
    
    const queryWithFilter = 
      !manual && hasFilter(config, filterValues) 
        ? appendFilter(config, filterValues) 
        : parsedQuery;
    
    try {
      const res = await api.request({
        url: 'charts:query',
        method: 'POST',
        data: {
          uid: schema?.['x-uid'],
          dataSource,
          collection,
          ...queryWithFilter,
          filter: removeUnparsableFilter(queryWithFilter.filter),
          // 处理维度和度量字段
          dimensions: (query?.dimensions || []).map(processDimension),
          measures: (query?.measures || []).map(processMeasure),
        },
      });
      return res?.data?.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },
  {
    defaultParams: [dataSource, collection, query],
    ready: ready && (!enabled || !!form),
  },
);
```

### 服务端查询处理流程

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/server/actions/query.ts:49`

```typescript
export const queryData = async (ctx: Context, next: Next) => {
  const { dataSource, collection, queryParams, fieldMap } = ctx.action.params.values;
  const db = getDB(ctx, dataSource) || ctx.db;
  const model = db.getModel(collection);
  const data = await model.findAll(queryParams);
  ctx.action.params.values = {
    data,
    fieldMap,
  };
  await next();
};
```

### 缓存处理机制

**源码位置**: `/packages/plugins/@nocobase/plugin-data-visualization/src/server/actions/query.ts:191`

```typescript
export const cacheMiddleware = async (ctx: Context, next: Next) => {
  const { uid, cache: cacheConfig, refresh } = ctx.action.params.values as QueryParams;
  const cache = ctx.app.cacheManager.getCache('data-visualization') as Cache;
  const useCache = cacheConfig?.enabled && uid;

  if (useCache && !refresh) {
    const data = await cache.get(uid);
    if (data) {
      ctx.body = data;
      return;
    }
  }
  await next();
  if (useCache) {
    await cache.set(uid, ctx.body, cacheConfig?.ttl * 1000);
  }
};
```

### 图表区块数据流示例

```typescript
// 1. 初始化图表区块
const service = useRequest(
  async (dataSource, collection, query) => {
    // 构建查询参数
    const queryParams = {
      measures: [
        { field: 'amount', aggregation: 'sum', alias: 'total_amount' }
      ],
      dimensions: [
        { field: 'category', alias: 'product_category' }
      ],
      filter: { status: 'active' },
      limit: 50
    };
    
    // 发送API请求
    const response = await api.request({
      url: 'charts:query',
      method: 'POST',
      data: {
        uid: 'chart-123',
        dataSource: 'main',
        collection: 'products',
        ...queryParams,
        cache: { enabled: true, ttl: 300 }
      }
    });
    
    return response.data.data;
  }
);

// 2. API请求
POST /api/charts:query
{
  "uid": "chart-123",
  "dataSource": "main",
  "collection": "products",
  "measures": [
    {
      "field": "amount",
      "aggregation": "sum",
      "alias": "total_amount"
    }
  ],
  "dimensions": [
    {
      "field": "category",
      "alias": "product_category"
    }
  ],
  "filter": { "status": "active" },
  "limit": 50,
  "cache": { "enabled": true, "ttl": 300 }
}

// 3. 数据响应
{
  "data": [
    {
      "product_category": "电子产品",
      "total_amount": 15000
    },
    {
      "product_category": "服装",
      "total_amount": 8500
    }
  ],
  "meta": {
    "cached": false,
    "timestamp": "2024-08-09T10:30:00Z"
  }
}

// 4. 刷新图表
service.refresh();

// 5. 带缓存刷新的查询
POST /api/charts:query
{
  "uid": "chart-123",
  "dataSource": "main",
  "collection": "products",
  "measures": [...],
  "dimensions": [...],
  "refresh": true  // 强制刷新缓存
}
```

---

## 总结

本文档详细描述了 NocoBase Charts Block 的完整操作体系，包括：

1. **图表数据查询**: 数据聚合、维度分析、筛选排序
2. **图表刷新操作**: 手动刷新、自动刷新、缓存管理
3. **图表配置操作**: 图表类型、样式配置、数据转换
4. **数据处理机制**: 字段解析、关联处理、数据转换
5. **图表区块配置**: 基础配置、SQL模式、缓存配置
6. **前端配置**: 操作按钮、渲染器配置、组件属性
7. **参数传递**: 从前端到后端的完整数据流

所有代码示例都基于 NocoBase 最新源码，确保了技术准确性和实用性。开发者可以基于这份文档准确理解和使用 NocoBase 的 Charts Block 功能。

---

**文档版本**: v1.0  
**更新日期**: 2024-08-09  
**NocoBase 版本**: 基于最新源码分析