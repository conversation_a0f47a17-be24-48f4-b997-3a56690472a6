# NocoBase Calendar Block 操作与 API 调用对应关系分析报告

## 概述

本报告深入分析 NocoBase Calendar Block（日历块）的各种操作如何通过内置 API 进行调用，重点关注操作配置、API 映射关系和实际应用场景。

## 1. Calendar Block 架构概览

### 1.1 核心组件结构

```
CalendarBlockProvider (日历数据提供器)
├── CalendarV2 (日历容器)
│   ├── ActionBar (操作栏)
│   │   ├── Filter (筛选)
│   │   ├── Add New (新增)
│   │   ├── Today (今天)
│   │   └── View Switcher (视图切换)
│   ├── BigCalendar (日历核心组件)
│   │   ├── Header (日期头部)
│   │   ├── Event (事件显示)
│   │   └── Toolbar (工具栏)
│   └── EventViewer (事件查看器)
└── Calendar.Settings (设置面板)
```

### 1.2 主要文件位置

- **块初始化器**: `/packages/plugins/@nocobase/plugin-calendar/src/client/CalendarBlockInitializer.tsx`
- **操作初始化器**: `/packages/plugins/@nocobase/plugin-calendar/src/client/CalendarActionInitializers.tsx`
- **UI Schema 创建**: `/packages/plugins/@nocobase/plugin-calendar/src/client/createCalendarBlockUISchema.ts`
- **主要组件**: `/packages/plugins/@nocobase/plugin-calendar/src/client/Calendar.tsx`
- **块提供器**: `/packages/plugins/@nocobase/plugin-calendar/src/client/CalendarBlockProvider.tsx`
- **设置组件**: `/packages/plugins/@nocobase/plugin-calendar/src/client/settings/`

## 2. 块级别操作与 API 映射

### 2.1 筛选操作 (Filter)

**操作配置**:
```typescript
{
  name: 'filter',
  title: "{{t('Filter')}}",
  Component: 'FilterActionInitializer',
  schema: {
    'x-align': 'left',
  },
}
```

**API 调用**:
- **端点**: `GET /{collection}:list`
- **参数**: 通过 `filter` 参数传递筛选条件
- **权限**: 无特殊权限要求

**实际应用**:
```typescript
// 筛选操作调用示例
const applyCalendarFilter = async (collectionName, filterParams) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:list`,
    method: 'GET',
    params: {
      filter: filterParams,
      paginate: false, // Calendar 默认不分页
    }
  });
};
```

### 2.2 新增操作 (Add New)

**操作配置**:
```typescript
{
  name: 'addNew',
  title: "{{t('Add new')}}",
  Component: 'CreateActionInitializer',
  schema: {
    'x-align': 'right',
    'x-decorator': 'ACLActionProvider',
    'x-acl-action-props': {
      skipScopeCheck: true,
    },
  },
  useVisible: () => useActionAvailable('create'),
}
```

**API 调用**:
- **端点**: `POST /{collection}:create`
- **权限**: `create`
- **特点**: 自动设置日期字段值

**实际应用**:
```typescript
// 新增事件调用示例
const createCalendarEvent = async (collectionName, eventData, startDate, endDate) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:create`,
    method: 'POST',
    data: {
      values: {
        ...eventData,
        [fieldNames.start]: startDate.toISOString(),
        [fieldNames.end]: endDate.toISOString(),
      },
      whitelist: ['title', 'description', 'startDate', 'endDate', 'priority'],
      updateAssociationValues: true,
    }
  });
};
```

### 2.3 今天操作 (Today)

**操作配置**:
```typescript
{
  name: 'today',
  title: "{{t('Today')}}",
  Component: 'Calendar.Today',
  schema: {
    'x-align': 'left',
  },
}
```

**API 调用**:
- **端点**: 无 API 调用（纯客户端操作）
- **功能**: 导航到当前日期

**实际应用**:
```typescript
// 今天操作调用示例（客户端）
const navigateToToday = () => {
  const [date, setDate] = useState(new Date());
  setDate(new Date());
};
```

## 3. 事件级别操作与 API 映射

### 3.1 快速创建事件 (Quick Create)

**操作配置**:
```typescript
// 通过日历槽位点击触发
onSelectSlot={(slotInfo) => {
  if (canCreate && enableQuickCreateEvent) {
    insertAddNewer(addNew);
    setVisibleAddNewer(true);
  }
}}
```

**API 调用**:
- **端点**: `POST /{collection}:create`
- **权限**: `create`
- **特点**: 预填充日期时间

**实际应用**:
```typescript
// 快速创建事件调用示例
const quickCreateEvent = async (collectionName, slotInfo) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:create`,
    method: 'POST',
    data: {
      values: {
        title: 'New Event',
        [fieldNames.start]: slotInfo.start.toISOString(),
        [fieldNames.end]: slotInfo.end.toISOString(),
      },
      whitelist: ['title', 'startDate', 'endDate'],
    }
  });
};
```

### 3.2 查看操作 (View)

**操作配置**:
```typescript
{
  type: 'void',
  title: '{{ t("View") }}',
  'x-designer': 'Action.Designer',
  'x-component': 'CalendarV2.Event',
  'x-action': 'view',
  'x-component-props': {
    openMode: 'drawer',
  }
}
```

**API 调用**:
- **端点**: `GET /{collection}:get/{id}`
- **权限**: `get`
- **参数**: `filterByTk` (主键值)

**实际应用**:
```typescript
// 查看事件调用示例
const viewCalendarEvent = async (collectionName, eventId) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:get`,
    method: 'GET',
    params: {
      filterByTk: eventId,
      fields: ['id', 'title', 'description', 'startDate', 'endDate'],
      appends: ['assignee'],
    }
  });
};
```

### 3.3 编辑操作 (Edit)

**操作配置**:
```typescript
// 通过事件查看器内部的表单系统处理
'x-component': 'CalendarV2.Event',
'x-read-pretty': true,
'x-component-props': {
  openMode: 'drawer',
}
```

**API 调用**:
- **端点**: `PUT /{collection}:update/{id}`
- **权限**: `update`
- **参数**: `filterByTk`, `values`

**实际应用**:
```typescript
// 编辑事件调用示例
const updateCalendarEvent = async (collectionName, eventId, updateData) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:update`,
    method: 'PUT',
    data: {
      filterByTk: eventId,
      values: updateData,
      whitelist: ['title', 'description', 'startDate', 'endDate', 'priority'],
      updateAssociationValues: true,
    }
  });
};
```

### 3.4 删除操作 (Delete)

**操作配置**:
```typescript
// 通过事件操作菜单或删除按钮触发
// 使用标准的 NocoBase 删除操作流程
```

**API 调用**:
- **端点**: `DELETE /{collection}:destroy/{id}`
- **权限**: `destroy`
- **参数**: `filterByTk`

**实际应用**:
```typescript
// 删除事件调用示例
const deleteCalendarEvent = async (collectionName, eventId) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:destroy`,
    method: 'DELETE',
    data: {
      filterByTk: eventId,
    }
  });
};
```

## 4. 事件移动操作与 API 映射

### 4.1 事件移动 (Event Move)

**操作配置**:
```typescript
// 通过 react-big-calendar 的 onEventDrop 回调处理
onEventDrop={(eventDropInfo) => {
  const { event, start, end } = eventDropInfo;
  const record = dataSource?.find((item) => item[fieldNames.id] === event.id);
  
  if (record) {
    // 更新事件的开始和结束时间
    record[fieldNames.start] = formatDate(dayjs(start));
    record[fieldNames.end] = formatDate(dayjs(end));
    
    // 调用 API 更新
    updateEvent(record);
  }
}}
```

**API 调用**:
- **端点**: `PUT /{collection}:update/{id}`
- **权限**: `update`
- **参数**: `filterByTk`, `values` (包含新的开始和结束时间)

**实际应用**:
```typescript
// 事件移动调用示例
const moveCalendarEvent = async (collectionName, eventId, newStart, newEnd) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:update`,
    method: 'PUT',
    data: {
      filterByTk: eventId,
      values: {
        [fieldNames.start]: newStart.toISOString(),
        [fieldNames.end]: newEnd.toISOString(),
      },
      whitelist: ['startDate', 'endDate'],
    }
  });
};
```

## 5. 重复事件操作与 API 映射

### 5.1 重复事件处理

**操作配置**:
```typescript
// 通过 cron 字段处理重复事件
if (cron === 'every_week') {
  // 每周重复
  let nextStart = start.clone().year(startDate.year()).month(startDate.month()).date(startDate.date()).day(start.day());
  while (nextStart.isBefore(endDate)) {
    if (push(nextStart.clone())) {
      break;
    }
    nextStart = nextStart.add(1, 'week');
  }
} else if (cron === 'every_month') {
  // 每月重复
  push(start.clone().year(dateM.year()).month(dateM.month()));
} else if (cron === 'every_year') {
  // 每年重复
  push(start.clone().year(dateM.year()));
}
```

**API 调用**:
- **端点**: `GET /{collection}:list`
- **参数**: 包含 `cron` 字段和 `exclude` 数组
- **处理**: 客户端根据 cron 表达式生成重复事件

**实际应用**:
```typescript
// 重复事件处理调用示例
const processRecurringEvents = async (collectionName, baseEvents) => {
  const processedEvents = [];
  
  for (const event of baseEvents) {
    if (event.cron) {
      // 根据 cron 表达式生成重复事件
      const recurringEvents = generateRecurringEvents(event);
      processedEvents.push(...recurringEvents);
    } else {
      processedEvents.push(event);
    }
  }
  
  return processedEvents;
};
```

### 5.2 重复事件删除

**操作配置**:
```typescript
// 删除重复事件时的选项处理
const onOk = async () => {
  if (value === 'all' || !cron) {
    // 删除所有重复事件
    await resource.destroy({ filterByTk });
  } else {
    // 删除特定事件（更新 exclude 数组）
    await resource.update({
      filterByTk,
      values: {
        exclude: (exclude || []).concat(value),
      },
    });
  }
};
```

**API 调用**:
- **端点**: `PUT /{collection}:update/{id}` 或 `DELETE /{collection}:destroy/{id}`
- **权限**: `update` 或 `destroy`
- **参数**: `exclude` 数组或 `filterByTk`

**实际应用**:
```typescript
// 重复事件删除调用示例
const deleteRecurringEvent = async (collectionName, eventId, deleteOption) => {
  const apiClient = useAPIClient();
  
  if (deleteOption === 'all') {
    // 删除所有重复事件
    return await apiClient.request({
      url: `/${collectionName}:destroy`,
      method: 'DELETE',
      data: {
        filterByTk: eventId,
      }
    });
  } else {
    // 更新 exclude 数组
    return await apiClient.request({
      url: `/${collectionName}:update`,
      method: 'PUT',
      data: {
        filterByTk: eventId,
        values: {
          exclude: [deleteOption],
        },
      }
    });
  }
};
```

## 6. Calendar Block 配置与创建

### 6.1 UI Schema 创建

**源码位置**: `/packages/plugins/@nocobase/plugin-calendar/src/client/createCalendarBlockUISchema.ts:13`

```typescript
export const createCalendarBlockUISchema = (options: {
  collectionName: string;
  dataSource: string;
  fieldNames?: Record<string, any>;
  params?: Record<string, any>;
}): ISchema => {
  const { collectionName, dataSource, fieldNames, params } = options;

  return {
    type: 'void',
    'x-acl-action': `${collectionName}:list`,
    'x-decorator': 'CalendarBlockProvider',
    'x-use-decorator-props': 'useCalendarBlockDecoratorProps',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      action: 'list',
      fieldNames: {
        id: 'id',
        title: 'title',
        start: ['startDate'],
        end: ['endDate'],
        ...fieldNames,
      },
      params: {
        paginate: false, // Calendar 默认不分页
        ...params,
      },
    },
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:calendar',
    'x-component': 'CardItem',
    properties: {
      calendar: {
        type: 'void',
        'x-component': 'CalendarV2',
        'x-use-component-props': 'useCalendarBlockProps',
        properties: {
          toolBar: {
            type: 'void',
            'x-component': 'CalendarV2.ActionBar',
            'x-component-props': {
              style: { marginBottom: 24 },
            },
            'x-initializer': 'calendar:configureActions',
          },
          event: {
            type: 'void',
            'x-component': 'CalendarV2.Event',
            'x-component-props': {
              openMode: 'drawer',
            },
            properties: {
              drawer: {
                type: 'void',
                'x-component': 'Action.Container',
                'x-component-props': {
                  className: 'nb-action-popup',
                },
                title: '{{ t("View record") }}',
                properties: {
                  tabs: {
                    type: 'void',
                    'x-component': 'Tabs',
                    'x-initializer': 'popup:addTab',
                    properties: {
                      tab1: {
                        type: 'void',
                        title: '{{t("Details")}}',
                        'x-component': 'Tabs.TabPane',
                        properties: {
                          grid: {
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'popup:common:addBlock',
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  };
};
```

### 6.2 通过 API 创建 Calendar Block

```typescript
// 创建 Calendar Block 调用示例
const createCalendarBlock = async (targetUid, blockConfig) => {
  const apiClient = useAPIClient();
  
  return await apiClient.request({
    url: `/uiSchemas:insertAdjacent/${targetUid}`,
    method: 'POST',
    data: {
      position: 'beforeEnd',
      schema: {
        type: 'void',
        title: blockConfig.title || 'Calendar',
        'x-decorator': 'CalendarBlockProvider',
        'x-component': 'CardItem',
        'x-settings': 'blockSettings:calendar',
        'x-decorator-props': {
          collection: blockConfig.collectionName,
          dataSource: blockConfig.dataSource,
          action: 'list',
          fieldNames: {
            id: 'id',
            title: blockConfig.titleField || 'title',
            start: [blockConfig.startField || 'startDate'],
            end: [blockConfig.endField || 'endDate'],
            colorFieldName: blockConfig.colorField || 'priority',
          },
          params: {
            paginate: false,
          },
        },
        properties: {
          calendar: {
            type: 'void',
            'x-component': 'CalendarV2',
            properties: {
              toolBar: {
                type: 'void',
                'x-component': 'CalendarV2.ActionBar',
                'x-initializer': 'calendar:configureActions',
              },
              event: {
                type: 'void',
                'x-component': 'CalendarV2.Event',
                'x-component-props': {
                  openMode: 'drawer',
                },
              },
            },
          },
        },
      }
    }
  });
};
```

## 7. 权限控制与 ACL

### 7.1 权限映射表

| 操作 | x-action | 权限要求 | ACL Provider |
|------|----------|----------|--------------|
| Filter | filter | 无 | 否 |
| Add New | create | create | 是 |
| Today | today | 无 | 否 |
| View Event | get | get | 是 |
| Edit Event | update | update | 是 |
| Delete Event | destroy | destroy | 是 |
| Move Event | update | update | 是 |
| Quick Create | create | create | 是 |

### 7.2 ACL 权限检查

```typescript
// 权限检查 Hook
const useActionAvailable = (action: string) => {
  const collection = useCollection();
  const { unavailableActions, availableActions } = collection?.options || {};
  
  if (availableActions) {
    return availableActions.includes(action);
  }
  
  if (unavailableActions) {
    return !unavailableActions.includes(action);
  }
  
  return true;
};

// 在 Calendar 组件中的使用
const { parseAction } = useACLRoleContext();
const collection = useCollection();
const canCreate = parseAction(`${collection.name}:create`);
```

## 8. 实际应用示例

### 8.1 完整的 Calendar Block 配置

```typescript
// 完整配置示例
const calendarConfig = {
  // 块配置
  title: '事件日历',
  collectionName: 'events',
  dataSource: 'main',
  
  // 字段配置
  fieldNames: {
    id: 'id',
    title: 'title',
    start: ['startDate'],
    end: ['endDate'],
    colorFieldName: 'priority',
  },
  
  // 块级别操作
  blockActions: [
    {
      name: 'filter',
      title: '筛选',
      enabled: true,
    },
    {
      name: 'addNew',
      title: '新增事件',
      enabled: true,
      requiresAuth: true,
    },
    {
      name: 'today',
      title: '今天',
      enabled: true,
    },
  ],
  
  // 事件字段
  fields: [
    {
      name: 'title',
      title: '事件标题',
      component: 'Input',
    },
    {
      name: 'description',
      title: '事件描述',
      component: 'Input.TextArea',
    },
    {
      name: 'startDate',
      title: '开始时间',
      component: 'DatePicker',
    },
    {
      name: 'endDate',
      title: '结束时间',
      component: 'DatePicker',
    },
    {
      name: 'priority',
      title: '优先级',
      component: 'Select',
      options: [
        { label: '高', value: 'high' },
        { label: '中', value: 'medium' },
        { label: '低', value: 'low' },
      ],
    },
  ],
};

// 创建 Calendar Block
const createEventCalendar = async (targetUid) => {
  return await createCalendarBlock(targetUid, calendarConfig);
};
```

### 8.2 事件操作处理

```typescript
// 事件操作处理示例
const handleEventOperations = async (operation, eventData) => {
  const apiClient = useAPIClient();
  const collectionName = 'events';
  
  try {
    switch (operation) {
      case 'create':
        return await apiClient.request({
          url: `/${collectionName}:create`,
          method: 'POST',
          data: {
            values: eventData,
            whitelist: ['title', 'description', 'startDate', 'endDate', 'priority'],
          }
        });
        
      case 'update':
        return await apiClient.request({
          url: `/${collectionName}:update`,
          method: 'PUT',
          data: {
            filterByTk: eventData.id,
            values: eventData,
            whitelist: ['title', 'description', 'startDate', 'endDate', 'priority'],
          }
        });
        
      case 'delete':
        return await apiClient.request({
          url: `/${collectionName}:destroy`,
          method: 'DELETE',
          data: {
            filterByTk: eventData.id,
          }
        });
        
      case 'move':
        return await apiClient.request({
          url: `/${collectionName}:update`,
          method: 'PUT',
          data: {
            filterByTk: eventData.id,
            values: {
              startDate: eventData.startDate,
              endDate: eventData.endDate,
            },
            whitelist: ['startDate', 'endDate'],
          }
        });
        
      default:
        throw new Error(`Unknown operation: ${operation}`);
    }
  } catch (error) {
    console.error('Event operation failed:', error);
    throw error;
  }
};
```

## 9. 性能优化建议

### 9.1 数据加载优化

```typescript
// 优化数据加载参数
const optimizedCalendarParams = {
  paginate: false, // 日历不分页
  fields: ['id', 'title', 'description', 'startDate', 'endDate', 'priority'],
  appends: ['assignee'], // 预加载关联数据
  filter: {}, // 应用筛选条件
  sort: ['startDate'], // 按开始时间排序
};
```

### 9.2 重复事件处理优化

```typescript
// 使用缓存优化重复事件生成
const cachedRecurringEvents = new Map();

const generateRecurringEvents = (baseEvent) => {
  const cacheKey = `${baseEvent.id}_${baseEvent.cron}`;
  
  if (cachedRecurringEvents.has(cacheKey)) {
    return cachedRecurringEvents.get(cacheKey);
  }
  
  const events = processRecurringEvent(baseEvent);
  cachedRecurringEvents.set(cacheKey, events);
  
  return events;
};
```

### 9.3 视图切换优化

```typescript
// 使用防抖优化视图切换
const debouncedViewChange = debounce(async (newView) => {
  // 更新视图状态
  setView(newView);
  
  // 可选：记录用户偏好
  await saveUserPreference('calendar_default_view', newView);
}, 300);
```

## 10. 错误处理和调试

### 10.1 常见错误及解决方案

| 错误类型 | 可能原因 | 解决方案 |
|---------|----------|----------|
| 401 Unauthorized | 认证失败 | 检查 JWT token 是否有效 |
| 403 Forbidden | 权限不足 | 检查用户权限配置 |
| 404 Not Found | 资源不存在 | 验证集合名称和字段配置 |
| 422 Unprocessable Entity | 参数错误 | 检查请求参数格式 |
| 500 Internal Server Error | 服务器错误 | 检查服务器日志 |

### 10.2 调试工具

```typescript
// 调试 Calendar API 调用
const debugCalendarApi = async (collectionName, operation, params) => {
  console.log('Calendar API Request:', {
    collectionName,
    operation,
    params,
    timestamp: new Date().toISOString(),
  });
  
  try {
    const apiClient = useAPIClient();
    const response = await apiClient.request({
      url: `/${collectionName}:${operation}`,
      method: operation === 'list' ? 'GET' : 'POST',
      data: params,
    });
    
    console.log('Calendar API Response:', {
      status: response.status,
      data: response.data,
      timestamp: new Date().toISOString(),
    });
    
    return response;
  } catch (error) {
    console.error('Calendar API Error:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      timestamp: new Date().toISOString(),
    });
    throw error;
  }
};
```

## 11. 总结

### 11.1 Calendar Block 操作特点

1. **可视化交互**: 支持拖拽移动事件、点击创建事件
2. **多视图支持**: 支持日、周、月视图切换
3. **重复事件**: 支持 cron 表达式的重复事件
4. **权限控制**: 细粒度的 ACL 权限管理
5. **响应式设计**: 适应不同屏幕尺寸

### 11.2 API 调用模式

1. **统一端点**: 所有操作都通过标准 RESTful API
2. **字段映射**: 支持灵活的字段映射配置
3. **重复事件处理**: 客户端处理重复事件生成
4. **实时更新**: 事件移动和编辑实时同步

### 11.3 最佳实践

1. **合理设置字段映射**: 选择合适的字段作为标题、开始/结束时间
2. **优化数据加载**: 使用适当的字段选择和排序
3. **权限配置**: 根据用户角色合理配置操作权限
4. **错误处理**: 实现完善的错误处理和回滚机制
5. **性能优化**: 对于大量数据使用分块加载和缓存

Calendar Block 为 NocoBase 提供了强大的可视化事件管理能力，通过拖拽操作和实时同步，可以满足各种日历式业务场景的需求。开发者可以基于这份文档准确理解和使用 Calendar Block 的各种功能。

---

**报告日期**: 2025-01-09  
**版本**: NocoBase 最新版本  
**作者**: Claude Code Assistant  

**注意**: 本报告基于 NocoBase 最新源码分析，代码示例已与实际实现保持一致。