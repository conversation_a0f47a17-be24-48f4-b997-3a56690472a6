# NocoBase Filter Block (Form/Collapse) 操作与 API 参数对应关系

本文档详细描述了 NocoBase 中 Filter Block (Form/Collapse) 的各种操作与其对应的 API 调用参数，为开发者提供完整的参考指南。

## 目录

1. [Filter Block 架构概述](#1-filter-block-架构概述)
2. [基础筛选操作](#2-基础筛选操作)
3. [筛选数据处理机制](#3-筛选数据处理机制)
4. [筛选目标管理](#4-筛选目标管理)
5. [高级筛选操作](#5-高级筛选操作)
6. [前端组件配置示例](#6-前端组件配置示例)
7. [参数传递机制](#7-参数传递机制)

---

## 1. Filter Block 架构概述

### 核心组件结构

```
FilterBlockProvider (筛选区块上下文提供者)
├── FilterFormBlockProvider (表单筛选区块)
│   ├── FormBlockProvider (表单区块提供者)
│   │   ├── FormV2 (主表单组件)
│   │   │   ├── Grid (表单字段容器)
│   │   │   │   └── Filter Fields (筛选表单字段)
│   │   │   └── ActionBar (表单操作栏)
│   │   │       └── Filter/Reset Actions (筛选/重置操作)
│   │   └── Filter Operators (筛选操作符收集)
│   └── DefaultValueProvider (默认值提供者)
└── FilterCollapseBlock (折叠筛选区块)
    ├── AssociationFilterProvider (关联筛选提供者)
    │   ├── AssociationFilter (关联筛选组件)
    │   └── AssociationFilterAction (关联筛选操作)
    └── CardItem (容器组件)
```

### 关键文件位置

- **FilterBlockProvider**: `/packages/core/client/src/filter-provider/FilterProvider.tsx`
- **FilterFormBlockProvider**: `/packages/core/client/src/block-provider/FilterFormBlockProvider.tsx`
- **Filter Actions**: `/packages/core/client/src/block-provider/hooks/index.ts:567-614`
- **Filter Utils**: `/packages/core/client/src/filter-provider/utils.ts`
- **AssociationFilter**: `/packages/core/client/src/schema-component/antd/association-filter/AssociationFilter.tsx`

---

## 2. 基础筛选操作

### Filter Form (表单筛选)

- **x-action**: `filter`
- **x-use-component-props**: `useFilterBlockActionProps`
- **API 参数**:
  ```typescript
  {
    filter: object,                   // 筛选条件
    page: number,                     // 页码
    pageSize: number,                 // 每页条数
    filters: {                        // 存储的筛选条件
      [blockUid]: filterObject        // 区块 UID 到筛选条件的映射
    }
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:567`

**实现逻辑**:
```typescript
export const useFilterBlockActionProps = () => {
  const { doFilter } = useDoFilter();
  const actionField = useField();
  actionField.data = actionField.data || {};

  return {
    async onClick() {
      actionField.data.loading = true;
      await doFilter();
      actionField.data.loading = false;
    },
  };
};
```

### Filter Reset (重置筛选)

- **x-action**: `reset`
- **x-use-component-props**: `useResetBlockActionProps`
- **API 参数**:
  ```typescript
  {
    page: number,                     // 重置到第一页
    filter: object,                   // 清除后的筛选条件
    filters: {                        // 更新后的筛选条件存储
      [blockUid]: undefined          // 清除指定区块的筛选条件
    }
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:601`

**实现逻辑**:
```typescript
export const useResetBlockActionProps = () => {
  const actionField = useField();
  const { doReset } = useDoReset();

  actionField.data = actionField.data || {};

  return {
    async onClick() {
      actionField.data.loading = true;
      await doReset();
      actionField.data.loading = false;
    },
  };
};
```

### Association Filter (关联筛选)

- **x-action**: `associateFilter`
- **x-component**: `AssociationFilter`
- **API 参数**:
  ```typescript
  {
    filter: object,                   // 关联筛选条件
    page: number,                     // 页码
    pageSize: number,                 // 每页条数
    fields?: string[],                // 返回字段
    appends?: string[],               // 关联字段
  }
  ```

**源码位置**: `/packages/core/client/src/schema-component/antd/association-filter/AssociationFilter.tsx`

---

## 3. 筛选数据处理机制

### 表单值转筛选条件

**源码位置**: `/packages/core/client/src/filter-provider/utils.ts:97`

```typescript
export const transformToFilter = (
  values: Record<string, any>,
  operators: Record<string, string>,
  getCollectionJoinField: (name: string) => CollectionFieldOptions_deprecated,
  collectionName: string,
) => {
  values = flatten(values, {
    breakOn({ value, path }) {
      // 特殊操作符处理
      if (
        [
          '$match', '$notMatch', '$anyOf', '$noneOf', '$childIn', '$childNotIn',
          '$dateBetween', '$in', '$notIn',
        ].includes(operators[path])
      ) {
        return true;
      }

      const collectionField = getCollectionJoinField(`${collectionName}.${path}`);

      // 日期时间字段特殊处理
      if (
        ['datetime', 'datetimeNoTz', 'date', 'unixTimestamp', 'createdAt', 'updatedAt'].includes(
          collectionField?.interface,
        )
      ) {
        return true;
      }

      // 关系字段特殊处理
      if (collectionField?.target) {
        if (Array.isArray(value)) {
          return true;
        }
        const targetKey = collectionField.targetKey || 'id';
        if (value && value[targetKey] != null) {
          return true;
        }
      }
      return false;
    },
  });

  const result = {
    $and: Object.keys(values)
      .map((key) => {
        let value = _.get(values, key);
        const collectionField = getCollectionJoinField(`${collectionName}.${key}`);

        // 关系字段处理
        if (
          collectionField?.target &&
          ['hasOne', 'hasMany', 'belongsTo', 'belongsToMany', 'belongsToArray'].includes(collectionField.type)
        ) {
          value = getValuesByPath(value, collectionField.targetKey || 'id');
          key = `${key}.${collectionField.targetKey || 'id'}`;

          if (collectionField?.interface === 'chinaRegion') {
            value = _.last(value);
          }
        }

        // 空值处理
        if (!value && value !== 0 && value !== false) {
          return null;
        }

        return {
          [key]: {
            [operators[key] || '$eq']: value,
          },
        };
      })
      .filter(Boolean),
  };

  return result;
};
```

### 筛选条件合并

**源码位置**: `/packages/core/client/src/filter-provider/utils.ts:29`

```typescript
export const mergeFilter = (filters: any[], op = '$and') => {
  const items = filters.filter((f) => {
    if (f && typeof f === 'object' && !Array.isArray(f)) {
      return Object.values(f).filter((v) => v !== undefined).length;
    }
  });
  if (items.length === 0) {
    return {};
  }
  if (items.length === 1) {
    return items[0];
  }
  return { [op]: items };
};
```

### 空值条件清理

**源码位置**: `/packages/core/client/src/schema-component/index.ts`

```typescript
export const removeNullCondition = (filter: any) => {
  if (!filter || typeof filter !== 'object') {
    return filter;
  }

  if (Array.isArray(filter)) {
    return filter.map(removeNullCondition).filter(Boolean);
  }

  const result: any = {};
  for (const [key, value] of Object.entries(filter)) {
    if (value === null || value === undefined || value === '') {
      continue;
    }
    if (typeof value === 'object') {
      const cleaned = removeNullCondition(value);
      if (Object.keys(cleaned).length > 0) {
        result[key] = cleaned;
      }
    } else {
      result[key] = value;
    }
  }

  return result;
};
```

---

## 4. 筛选目标管理

### 筛选目标查找

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:442`

```typescript
export interface FilterTarget {
  targets?: {
    /** field uid */
    uid: string;
    /** associated field */
    field?: string;
  }[];
  /** 当前筛选区块的 UID */
  uid: string;
}

export const findFilterTargets = (fieldSchema: Schema) => {
  const filterTargets = fieldSchema?.['x-filter-targets'] || [];
  const uid = fieldSchema['x-uid'];
  
  return {
    targets: filterTargets,
    uid,
  };
};
```

### 数据区块收集

**源码位置**: `/packages/core/client/src/filter-provider/FilterProvider.tsx:119`

```typescript
export const DataBlockCollector = ({
  children,
  params,
}: {
  children: React.ReactNode;
  params?: { filter?: FilterParam };
}) => {
  const collection = useCollection();
  const { recordDataBlocks } = useFilterBlock();
  const { getDataBlockRequest } = useDataBlockRequestGetter();
  const field = useField();
  const fieldSchema = useFieldSchema();
  const associatedFields = useAssociatedFields();
  const container = useRef<HTMLDivElement | null>(null);
  const dataLoadingMode = useDataLoadingMode();

  const addBlockToDataBlocks = useCallback(() => {
    const service = getDataBlockRequest();
    recordDataBlocks({
      uid: fieldSchema['x-uid'],
      title: field.componentProps.title,
      doFilter: service.runAsync as any,
      collection,
      associatedFields,
      foreignKeyFields: collection?.getFields('isForeignKey') as ForeignKeyField[],
      defaultFilter: params?.filter || {},
      service,
      dom: container.current,
      dataLoadingMode,
      clearFilter(uid: string) {
        const param = this.service.params?.[0] || {};
        const storedFilter = this.service.params?.[1]?.filters || {};
        delete storedFilter[uid];
        const mergedFilter = mergeFilter([
          ...Object.values(storedFilter).map((filter) => removeNullCondition(filter)),
          params?.filter || {},
        ]);

        this.service.run(
          {
            ...param,
            page: 1,
            filter: mergedFilter,
          },
          { filters: storedFilter },
        );
      },
      clearData() {
        this.service.mutate(undefined);
      },
      clearSelection() {
        if (field) {
          field.data?.clearSelectedRowKeys?.();
        }
      },
    });
  }, [/* dependencies */]);

  useEffect(() => {
    if (shouldApplyFilter) addBlockToDataBlocks();
  }, [addBlockToDataBlocks, shouldApplyFilter]);

  return <div ref={container}>{children}</div>;
};
```

---

## 5. 高级筛选操作

### doFilter 核心逻辑

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:483`

```typescript
const useDoFilter = () => {
  const form = useForm();
  const { getDataBlocks } = useFilterBlock();
  const cm = useCollectionManager();
  const { getOperators } = useOperators();
  const fieldSchema = useFieldSchema();
  const { name } = useCollection();
  const { targets = [], uid } = useMemo(() => findFilterTargets(fieldSchema), [fieldSchema]);
  const getFilterFromCurrentForm = useCallback(() => {
    return removeNullCondition(transformToFilter(form.values, getOperators(), cm.getCollectionField.bind(cm), name));
  }, [form.values, cm, getOperators, name]);

  const doFilter = useCallback(
    async ({ doNothingWhenFilterIsEmpty = false } = {}) => {
      try {
        // 收集 filter 的值
        await Promise.all(
          getDataBlocks().map(async (block) => {
            const target = targets.find((target) => target.uid === block.uid);
            if (!target) return;

            const param = block.service.params?.[0] || {};
            // 保留原有的 filter
            const storedFilter = block.service.params?.[1]?.filters || {};

            // 由当前表单转换而来的 filter
            storedFilter[uid] = getFilterFromCurrentForm();
            const mergedFilter = mergeFilter([
              ...Object.values(storedFilter).map((filter) => removeNullCondition(filter)),
              block.defaultFilter,
            ]);

            if (_.isEmpty(storedFilter[uid])) {
              block.clearSelection?.();
            }

            if (doNothingWhenFilterIsEmpty && _.isEmpty(storedFilter[uid])) {
              return;
            }

            if (block.dataLoadingMode === 'manual' && _.isEmpty(storedFilter[uid])) {
              return block.clearData();
            }

            // 存储当前的筛选条件，供其它筛选区块使用
            _.set(block.service.params, '1.filters', storedFilter);
            return block.doFilter(
              {
                ...param,
                page: 1,
                filter: mergedFilter,
              },
              { filters: storedFilter },
            );
          }),
        );
      } catch (error) {
        console.error(error);
      }
    },
    [getDataBlocks, getFilterFromCurrentForm, targets, uid],
  );

  // 首次渲染时自动执行筛选
  useEffect(() => {
    setTimeout(() => {
      doFilter({ doNothingWhenFilterIsEmpty: true });
    }, 500);
  }, [doFilter]);

  return {
    doFilter,
    getFilterFromCurrentForm,
  };
};
```

### doReset 核心逻辑

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:581`

```typescript
const useDoReset = () => {
  const form = useForm();
  const fieldSchema = useFieldSchema();
  const { getDataBlocks } = useFilterBlock();
  const { targets, uid } = findFilterTargets(fieldSchema);
  const { doFilter, getFilterFromCurrentForm } = useDoFilter();

  return {
    doReset: async () => {
      await form.reset(undefined, {
        forceClear: !!fieldSchema?.['x-component-props']?.clearDefaultValue,
      });
      if (_.isEmpty(getFilterFromCurrentForm())) {
        return doReset({ getDataBlocks, targets, uid });
      }
      await doFilter();
    },
  };
};

async function doReset({
  getDataBlocks,
  targets,
  uid,
}: {
  getDataBlocks: () => DataBlock[];
  targets: {
    uid: string;
    field?: string;
  }[];
  uid: string;
}) {
  try {
    await Promise.all(
      getDataBlocks().map(async (block) => {
        const target = targets.find((target) => target.uid === block.uid);
        if (!target) return;
        block.clearSelection?.();
        if (block.dataLoadingMode === 'manual') {
          return block.clearData();
        }
        const param = block.service.params?.[0] || {};
        // 保留原有的 filter
        const storedFilter = block.service.params?.[1]?.filters || {};
        delete storedFilter[uid];
        const mergedFilter = mergeFilter([...Object.values(storedFilter), block.defaultFilter]);
        return block.doFilter(
          {
            ...param,
            page: 1,
            filter: mergedFilter,
          },
          { filters: storedFilter },
        );
      }),
    );
  } catch (error) {
    console.error(error);
  }
}
```

---

## 6. 前端组件配置示例

### 表单筛选区块配置

```typescript
{
  type: 'void',
  'x-decorator': 'FilterFormBlockProvider',
  'x-use-decorator-props': 'useFilterFormBlockDecoratorProps',
  'x-component': 'CardItem',
  'x-settings': 'blockSettings:filterForm',
  'x-filter-targets': [
    {
      uid: 'target-block-uid',
      field: 'associatedFieldName',
    }
  ],
  properties: {
    form: {
      'x-component': 'FormV2',
      'x-use-component-props': 'useFilterFormBlockProps',
      properties: {
        grid: {
          'x-component': 'Grid',
          'x-initializer': 'filterForm:configureFields',
          properties: {
            // 筛选字段配置
            fieldName: {
              type: 'string',
              title: '字段名称',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-collection-field': 'collectionName.fieldName',
            },
            status: {
              type: 'string',
              title: '状态',
              'x-decorator': 'FormItem',
              'x-component': 'Select',
              'x-collection-field': 'collectionName.status',
            },
          },
        },
        actions: {
          'x-component': 'ActionBar',
          'x-initializer': 'filterForm:configureActions',
          properties: {
            submit: {
              type: 'void',
              title: '筛选',
              'x-action': 'filter',
              'x-component': 'Action',
              'x-use-component-props': 'useFilterBlockActionProps',
              'x-component-props': {
                type: 'primary',
                icon: 'FilterOutlined',
              },
            },
            reset: {
              type: 'void',
              title: '重置',
              'x-action': 'reset',
              'x-component': 'Action',
              'x-use-component-props': 'useResetBlockActionProps',
              'x-component-props': {
                icon: 'ReloadOutlined',
              },
            },
          },
        },
      },
    },
  },
}
```

### 折叠筛选区块配置

```typescript
{
  type: 'void',
  'x-decorator': 'AssociationFilter.Provider',
  'x-use-decorator-props': 'useCollapseBlockDecoratorProps',
  'x-decorator-props': {
    collection: 'collectionName',
    dataSource: 'main',
    blockType: 'filter',
    associationFilterStyle: {
      width: '100%',
    },
    name: 'filter-collapse',
  },
  'x-toolbar': 'BlockSchemaToolbar',
  'x-settings': 'blockSettings:filterCollapse',
  'x-component': 'CardItem',
  'x-filter-targets': [
    {
      uid: 'target-block-uid',
      field: 'associatedFieldName',
    }
  ],
  properties: {
    [uid()]: {
      type: 'void',
      'x-action': 'associateFilter',
      'x-initializer': 'filterCollapse:configureFields',
      'x-component': 'AssociationFilter',
    },
  },
}
```

### 筛选字段配置

```typescript
{
  type: 'string',
  title: '用户名',
  'x-decorator': 'FormItem',
  'x-component': 'Input',
  'x-collection-field': 'users.username',
  'x-settings': 'fieldSettings:FormItem',
  'x-component-props': {
    placeholder: '请输入用户名',
  },
}
```

---

## 7. 参数传递机制

### 筛选参数流程

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:495`

```typescript
// 1. 收集表单值
const getFilterFromCurrentForm = useCallback(() => {
  return removeNullCondition(transformToFilter(form.values, getOperators(), cm.getCollectionField.bind(cm), name));
}, [form.values, cm, getOperators, name]);

// 2. 应用筛选条件
const doFilter = useCallback(
  async ({ doNothingWhenFilterIsEmpty = false } = {}) => {
    await Promise.all(
      getDataBlocks().map(async (block) => {
        const target = targets.find((target) => target.uid === block.uid);
        if (!target) return;

        const param = block.service.params?.[0] || {};
        const storedFilter = block.service.params?.[1]?.filters || {};

        // 存储当前筛选条件
        storedFilter[uid] = getFilterFromCurrentForm();
        const mergedFilter = mergeFilter([
          ...Object.values(storedFilter).map((filter) => removeNullCondition(filter)),
          block.defaultFilter,
        ]);

        // 调用数据区块的筛选方法
        return block.doFilter(
          {
            ...param,
            page: 1,
            filter: mergedFilter,
          },
          { filters: storedFilter },
        );
      }),
    );
  },
  [getDataBlocks, getFilterFromCurrentForm, targets, uid],
);
```

### 筛选操作符映射

**源码位置**: `/packages/core/client/src/filter-provider/utils.ts:97`

```typescript
// 表单值到筛选条件的转换
{
  [key]: {
    [operators[key] || '$eq']: value,
  }
}

// 示例转换
// 输入: { username: 'admin', status: 'active' }
// 输出: { 
//   $and: [
//     { username: { $eq: 'admin' } },
//     { status: { $eq: 'active' } }
//   ]
// }
```

### API 调用参数映射

| 操作类型 | 前端 Action | API 方法 | 关键参数 |
|---------|-------------|----------|----------|
| 表单筛选 | `filter` | `resource.list()` | `filter`, `page`, `pageSize` |
| 重置筛选 | `reset` | `resource.list()` | `filter`, `page` |
| 关联筛选 | `associateFilter` | `resource.list()` | `filter`, `fields`, `appends` |

### 筛选条件存储格式

```typescript
// 服务参数中的筛选条件存储
{
  params: [
    {
      page: 1,
      pageSize: 20,
      filter: { $and: [...] }  // 合并后的筛选条件
    },
    {
      filters: {
        'filter-block-uid-1': { username: { $eq: 'admin' } },
        'filter-block-uid-2': { status: { $eq: 'active' } },
      }
    }
  ]
}
```

---

## 总结

本文档详细描述了 NocoBase Filter Block (Form/Collapse) 的完整操作体系，包括：

1. **基础筛选操作**: 表单筛选、重置筛选、关联筛选
2. **数据处理机制**: 表单值转换、筛选条件合并、空值清理
3. **筛选目标管理**: 目标查找、数据区块收集、筛选应用
4. **高级操作**: doFilter/doReset 核心逻辑、自动筛选执行
5. **前端配置**: 表单筛选区块、折叠筛选区块、字段配置
6. **参数传递**: 从前端到后端的完整筛选数据流

所有代码示例都基于 NocoBase 最新源码，确保了技术准确性和实用性。开发者可以基于这份文档准确理解和使用 NocoBase 的 Filter Block 功能。

---

**文档版本**: v1.0  
**更新日期**: 2025-08-09  
**NocoBase 版本**: 基于最新源码分析