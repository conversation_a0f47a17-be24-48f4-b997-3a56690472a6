#!/usr/bin/env node

// 测试关联字段数据
const { NocoBaseClient } = require('./mcp-server-nocobase/dist/client.js');

async function testRelationData() {
  console.log('🚀 开始测试关联字段数据...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 1. 获取现有数据
    console.log('📋 步骤1: 获取现有数据');
    const teachers = await client.listRecords('test_teachers');
    const courses = await client.listRecords('test_courses');
    const students = await client.listRecords('test_students');
    
    console.log(`✅ 教师: ${teachers.data.length} 个`);
    console.log(`✅ 课程: ${courses.data.length} 个`);
    console.log(`✅ 学生: ${students.data.length} 个`);
    console.log();

    // 2. 测试belongsTo关联 - 学生选择课程
    console.log('📋 步骤2: 测试belongsTo关联 - 学生选择课程');
    if (students.data.length > 0 && courses.data.length > 0) {
      const student = students.data[0];
      const course = courses.data[0];
      
      try {
        const updatedStudent = await client.updateRecord('test_students', student.id, {
          courseId: course.id  // 设置外键
        });
        console.log(`✅ 成功设置学生 ${student.name} 选择课程 ${course.name}`);
        console.log(`   学生ID: ${student.id}, 课程ID: ${course.id}`);
      } catch (error) {
        console.log(`❌ 设置学生课程关联失败: ${error.message}`);
      }
    }
    console.log();

    // 3. 创建学生档案测试hasOne关联
    console.log('📋 步骤3: 测试hasOne关联 - 创建学生档案');
    if (students.data.length > 0) {
      const student = students.data[0];
      
      try {
        // 先为档案集合添加bio字段
        try {
          await client.createField('test_profiles', {
            name: 'bio',
            type: 'text',
            interface: 'textarea',
            uiSchema: {
              type: 'string',
              title: '个人简介',
              'x-component': 'Input.TextArea'
            }
          });
          console.log('✅ 成功创建bio字段');
        } catch (error) {
          if (error.message.includes('already exists')) {
            console.log('ℹ️  bio字段已存在');
          } else {
            console.log(`❌ 创建bio字段失败: ${error.message}`);
          }
        }

        const profile = await client.createRecord('test_profiles', {
          studentId: student.id,  // 设置外键
          bio: `这是${student.name}的个人档案`
        });
        console.log(`✅ 成功创建学生档案: ID ${profile.id}`);
        console.log(`   关联学生: ${student.name} (ID: ${student.id})`);
      } catch (error) {
        console.log(`❌ 创建学生档案失败: ${error.message}`);
      }
    }
    console.log();

    // 4. 创建标签测试belongsToMany关联
    console.log('📋 步骤4: 测试belongsToMany关联 - 创建课程标签');
    
    // 先为标签集合添加name字段
    try {
      await client.createField('test_tags', {
        name: 'name',
        type: 'string',
        interface: 'input',
        uiSchema: {
          type: 'string',
          title: '标签名称',
          'x-component': 'Input'
        }
      });
      console.log('✅ 成功创建标签name字段');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  标签name字段已存在');
      } else {
        console.log(`❌ 创建标签name字段失败: ${error.message}`);
      }
    }

    // 创建一些标签
    const tags = [];
    const tagNames = ['数学', '基础课程', '必修课'];
    
    for (const tagName of tagNames) {
      try {
        const tag = await client.createRecord('test_tags', {
          name: tagName
        });
        tags.push(tag);
        console.log(`✅ 成功创建标签: ${tagName} (ID: ${tag.id})`);
      } catch (error) {
        console.log(`❌ 创建标签 ${tagName} 失败: ${error.message}`);
      }
    }

    // 为课程添加标签（通过中间表）
    if (courses.data.length > 0 && tags.length > 0) {
      const course = courses.data[0];
      const tag = tags[0];
      
      try {
        const relation = await client.createRecord('test_courses_tags', {
          courseId: course.id,
          tagId: tag.id
        });
        console.log(`✅ 成功关联课程 ${course.name} 和标签 ${tag.name}`);
      } catch (error) {
        console.log(`❌ 创建课程标签关联失败: ${error.message}`);
      }
    }
    console.log();

    // 5. 验证关联数据
    console.log('📋 步骤5: 验证关联数据');
    
    // 查询带关联数据的学生
    try {
      const studentWithRelations = await client.getRecord('test_students', students.data[0].id, ['course', 'profile']);
      console.log('✅ 学生关联数据:');
      console.log(`   学生: ${studentWithRelations.name}`);
      console.log(`   选择课程: ${studentWithRelations.course?.name || '未选择'}`);
      console.log(`   档案: ${studentWithRelations.profile?.bio || '无档案'}`);
    } catch (error) {
      console.log(`❌ 查询学生关联数据失败: ${error.message}`);
    }

    // 查询带关联数据的课程
    try {
      const courseWithRelations = await client.getRecord('test_courses', courses.data[0].id, ['students', 'tags']);
      console.log('✅ 课程关联数据:');
      console.log(`   课程: ${courseWithRelations.name}`);
      console.log(`   选课学生数: ${courseWithRelations.students?.length || 0}`);
      console.log(`   标签数: ${courseWithRelations.tags?.length || 0}`);
    } catch (error) {
      console.log(`❌ 查询课程关联数据失败: ${error.message}`);
    }

    console.log('\n🎉 关联字段数据测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

testRelationData().catch(console.error);
