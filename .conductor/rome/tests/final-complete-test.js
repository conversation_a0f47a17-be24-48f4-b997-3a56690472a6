#!/usr/bin/env node

// 最终完整的关联关系测试
const { NocoBaseClient } = require('./mcp-server-nocobase/dist/client.js');

async function finalCompleteTest() {
  console.log('🎓 最终完整的学生选课系统关联关系测试\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    console.log('📊 === 完整的关联关系图 ===');
    console.log('');
    console.log('👨‍🏫 教师 (test_teachers)');
    console.log('   ↓ hasMany');
    console.log('📚 课程 (test_courses)');
    console.log('   ↓ hasMany        ↘ belongsToMany');
    console.log('👨‍🎓 学生 (test_students)    🏷️  标签 (test_tags)');
    console.log('   ↓ hasOne');
    console.log('📋 档案 (test_profiles)');
    console.log('');

    // 查询所有数据并显示完整的关联关系
    console.log('📋 === 完整数据展示 ===\n');

    // 1. 教师及其课程
    console.log('👨‍🏫 教师信息:');
    const teachers = await client.listRecords('test_teachers');
    for (const teacher of teachers.data) {
      const teacherWithCourses = await client.getRecord('test_teachers', teacher.id, ['courses']);
      console.log(`  • ${teacher.name} (ID: ${teacher.id})`);
      if (teacherWithCourses.courses && teacherWithCourses.courses.length > 0) {
        teacherWithCourses.courses.forEach(course => {
          console.log(`    📚 教授: ${course.name}`);
        });
      } else {
        console.log(`    📚 暂无课程`);
      }
    }
    console.log();

    // 2. 课程及其关联信息
    console.log('📚 课程信息:');
    const courses = await client.listRecords('test_courses');
    for (const course of courses.data) {
      const courseWithAll = await client.getRecord('test_courses', course.id, ['teacher', 'students', 'tags']);
      console.log(`  • ${course.name} (ID: ${course.id})`);
      
      if (courseWithAll.teacher) {
        console.log(`    👨‍🏫 授课教师: ${courseWithAll.teacher.name}`);
      }
      
      if (courseWithAll.students && courseWithAll.students.length > 0) {
        console.log(`    👨‍🎓 选课学生 (${courseWithAll.students.length}人):`);
        courseWithAll.students.forEach(student => {
          console.log(`      - ${student.name}`);
        });
      } else {
        console.log(`    👨‍🎓 暂无学生选课`);
      }
      
      if (courseWithAll.tags && courseWithAll.tags.length > 0) {
        console.log(`    🏷️  课程标签:`);
        courseWithAll.tags.forEach(tag => {
          console.log(`      - ${tag.name}`);
        });
      } else {
        console.log(`    🏷️  暂无标签`);
      }
    }
    console.log();

    // 3. 学生及其关联信息
    console.log('👨‍🎓 学生信息:');
    const students = await client.listRecords('test_students');
    for (const student of students.data) {
      const studentWithAll = await client.getRecord('test_students', student.id, ['course', 'profile']);
      console.log(`  • ${student.name} (ID: ${student.id})`);
      
      if (studentWithAll.course) {
        console.log(`    📚 选修课程: ${studentWithAll.course.name}`);
      } else {
        console.log(`    📚 暂未选课`);
      }
      
      if (studentWithAll.profile) {
        console.log(`    📋 个人档案: ${studentWithAll.profile.bio}`);
      } else {
        console.log(`    📋 暂无档案`);
      }
    }
    console.log();

    // 4. 标签信息
    console.log('🏷️  标签信息:');
    const tags = await client.listRecords('test_tags');
    for (const tag of tags.data) {
      console.log(`  • ${tag.name} (ID: ${tag.id})`);
    }
    console.log();

    // 5. 中间表信息
    console.log('🔄 中间表 (test_courses_tags):');
    try {
      const relations = await client.listRecords('test_courses_tags');
      if (relations.data.length > 0) {
        relations.data.forEach(relation => {
          console.log(`  • 课程ID: ${relation.courseId} ↔ 标签ID: ${relation.tagId}`);
        });
      } else {
        console.log('  暂无关联数据');
      }
    } catch (error) {
      console.log(`  ❌ 查询失败: ${error.message}`);
    }
    console.log();

    // 6. 关联类型总结
    console.log('📊 === 关联类型总结 ===');
    console.log('✅ belongsTo (多对一):');
    console.log('   • 课程 → 教师 (course.teacher)');
    console.log('   • 学生 → 课程 (student.course)');
    console.log('');
    console.log('✅ hasMany (一对多):');
    console.log('   • 教师 → 课程 (teacher.courses)');
    console.log('   • 课程 → 学生 (course.students)');
    console.log('');
    console.log('✅ hasOne (一对一):');
    console.log('   • 学生 → 档案 (student.profile)');
    console.log('');
    console.log('✅ belongsToMany (多对多):');
    console.log('   • 课程 → 标签 (course.tags)');
    console.log('   • 通过中间表: test_courses_tags');
    console.log('');

    console.log('🎉 === 测试完成！所有关联关系都正常工作！ ===');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

finalCompleteTest().catch(console.error);
