#!/usr/bin/env node

/**
 * 简单测试 MCP 工具
 */

const { spawn } = require('child_process');
const path = require('path');

const config = {
  serverUrl: 'https://app.dev.orb.local/api',
  appId: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA'
};

async function testMCP() {
  console.log('🔍 测试 MCP 工具...\n');

  try {
    // 1. 测试列出集合
    console.log('📋 1. 测试列出集合...');
    const listResult = await callMCPTool('list_collections', {});
    if (listResult.result && listResult.result.isError) {
      console.log('❌ 列出集合失败');
      console.log('错误详情:', JSON.stringify(listResult.result.content, null, 2));
    } else {
      console.log('✅ 成功获取集合列表');
      console.log('结果:', JSON.stringify(listResult, null, 2));
    }
    console.log('');

    // 2. 测试创建简单集合
    const testCollectionName = 'test_mcp_' + Date.now();
    console.log(`📝 2. 测试创建集合: ${testCollectionName}...`);
    const createResult = await callMCPTool('create_collection', {
      name: testCollectionName,
      title: 'MCP测试集合',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    });
    console.log('✅ 成功创建集合');
    console.log('结果:', createResult);
    console.log('');

    // 3. 测试添加字段
    console.log(`📝 3. 测试添加字段...`);
    const fieldResult = await callMCPTool('create_field', {
      collection: testCollectionName,
      name: 'test_name',
      type: 'string',
      interface: 'input',
      description: '测试名称字段'
    });
    console.log('✅ 成功添加字段');
    console.log('结果:', fieldResult);
    console.log('');

    // 4. 测试添加关联字段
    console.log(`📝 4. 测试添加关联字段...`);
    
    // 先创建目标集合
    const targetCollectionName = 'test_target_' + Date.now();
    await callMCPTool('create_collection', {
      name: targetCollectionName,
      title: 'MCP目标集合',
      autoGenId: true
    });
    
    // 创建关联字段
    const relationResult = await callMCPTool('create_field', {
      collection: testCollectionName,
      name: 'target_relation',
      type: 'belongsTo',
      interface: 'm2o',
      description: '关联字段测试',
      target: targetCollectionName,
      foreignKey: 'targetId',
      targetKey: 'id'
    });
    console.log('✅ 成功添加关联字段');
    console.log('结果:', relationResult);
    console.log('');

    console.log('🎉 MCP 工具测试成功！');

  } catch (error) {
    console.error('❌ MCP 测试失败:', error.message);
  }
}

// 调用 MCP 工具
function callMCPTool(tool, params) {
  return new Promise((resolve, reject) => {
    const mcpProcess = spawn('node', [
      path.join(__dirname, 'mcp-server-nocobase/dist/index.js'),
      '--base-url', config.serverUrl,
      '--token', config.token,
      '--app', config.appId
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let error = '';

    mcpProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    mcpProcess.stderr.on('data', (data) => {
      error += data.toString();
    });

    mcpProcess.on('close', (code) => {
      if (code === 0) {
        try {
          // 尝试解析 JSON 响应
          const lines = output.trim().split('\n');
          const lastLine = lines[lines.length - 1];
          if (lastLine.startsWith('{')) {
            const result = JSON.parse(lastLine);
            resolve(result);
          } else {
            resolve({ success: true, output: output.trim() });
          }
        } catch (e) {
          resolve({ success: true, output: output.trim() });
        }
      } else {
        reject(new Error(error || `Process exited with code ${code}`));
      }
    });

    // 发送 MCP 请求
    const request = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/call',
      params: {
        name: tool,
        arguments: params
      }
    };

    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    mcpProcess.stdin.end();
  });
}

// 运行测试
if (require.main === module) {
  testMCP().catch(console.error);
}

module.exports = { testMCP };
