#!/usr/bin/env node

// 检查当前的关联关系
const { NocoBaseClient } = require('./mcp-server-nocobase/dist/client.js');

async function checkCurrentRelations() {
  console.log('🔍 检查当前的关联关系...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    const collections = ['test_students', 'test_courses', 'test_teachers', 'test_profiles', 'test_tags'];
    
    for (const collectionName of collections) {
      console.log(`📋 ${collectionName} 集合的字段:`);
      try {
        const collection = await client.getCollection(collectionName);
        
        if (collection.fields && collection.fields.length > 0) {
          collection.fields.forEach(field => {
            const fieldInfo = `  • ${field.name} (${field.type})`;
            if (field.interface) {
              console.log(`${fieldInfo} - ${field.interface}`);
            } else {
              console.log(fieldInfo);
            }
            
            // 显示关联字段的详细信息
            if (['belongsTo', 'hasMany', 'hasOne', 'belongsToMany'].includes(field.type)) {
              console.log(`    🔗 关联类型: ${field.type}`);
              if (field.target) console.log(`    🎯 目标集合: ${field.target}`);
              if (field.foreignKey) console.log(`    🔑 外键: ${field.foreignKey}`);
              if (field.sourceKey) console.log(`    🔑 源键: ${field.sourceKey}`);
              if (field.targetKey) console.log(`    🔑 目标键: ${field.targetKey}`);
              if (field.through) console.log(`    🔄 中间表: ${field.through}`);
              if (field.otherKey) console.log(`    🔑 其他键: ${field.otherKey}`);
            }
          });
        } else {
          console.log('  无字段');
        }
      } catch (error) {
        console.log(`  ❌ 获取失败: ${error.message}`);
      }
      console.log();
    }

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error.message);
  }
}

checkCurrentRelations().catch(console.error);
