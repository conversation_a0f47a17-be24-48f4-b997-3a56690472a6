#!/usr/bin/env node

// 测试MCP工具是否可以直接调用
const { NocoBaseClient } = require('./mcp-server-nocobase/dist/client.js');

async function testMCPTools() {
  console.log('🚀 开始测试MCP工具功能...\n');

  // 创建NocoBase客户端
  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 测试1: 列出集合
    console.log('📋 测试1: 列出所有集合');
    const collections = await client.listCollections();
    console.log(`✅ 找到 ${collections.length} 个集合:`);
    collections.forEach(c => console.log(`  • ${c.name} (${c.title || 'No title'})`));
    console.log();

    // 测试2: 创建教师集合（如果不存在）
    console.log('📋 测试2: 创建教师集合');
    try {
      const teacherCollection = await client.createCollection({
        name: 'test_teachers',
        title: '测试教师',
        description: '教师信息集合',
        autoGenId: true,
        createdAt: true,
        updatedAt: true
      });
      console.log(`✅ 成功创建教师集合: ${teacherCollection.name}`);
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️  教师集合已存在，跳过创建');
      } else {
        console.log(`❌ 创建教师集合失败: ${error.message}`);
      }
    }
    console.log();

    // 测试3: 为教师集合添加姓名字段
    console.log('📋 测试3: 为教师集合添加姓名字段');
    try {
      const nameField = await client.createField('test_teachers', {
        name: 'name',
        type: 'string',
        interface: 'input',
        uiSchema: {
          type: 'string',
          title: '教师姓名',
          'x-component': 'Input'
        }
      });
      console.log(`✅ 成功创建姓名字段: ${nameField.name}`);
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️  姓名字段已存在，跳过创建');
      } else {
        console.log(`❌ 创建姓名字段失败: ${error.message}`);
      }
    }
    console.log();

    // 测试4: 创建教师记录
    console.log('📋 测试4: 创建教师记录');
    try {
      const teacher1 = await client.createRecord('test_teachers', {
        name: '张教授'
      });
      console.log(`✅ 成功创建教师记录: ID ${teacher1.id}, 姓名: ${teacher1.name}`);

      const teacher2 = await client.createRecord('test_teachers', {
        name: '李老师'
      });
      console.log(`✅ 成功创建教师记录: ID ${teacher2.id}, 姓名: ${teacher2.name}`);
    } catch (error) {
      console.log(`❌ 创建教师记录失败: ${error.message}`);
    }
    console.log();

    // 测试5: 列出教师记录
    console.log('📋 测试5: 列出教师记录');
    try {
      const teacherRecords = await client.listRecords('test_teachers');
      console.log(`✅ 找到 ${teacherRecords.data.length} 个教师记录:`);
      teacherRecords.data.forEach(teacher => {
        console.log(`  • ID: ${teacher.id}, 姓名: ${teacher.name}`);
      });
    } catch (error) {
      console.log(`❌ 列出教师记录失败: ${error.message}`);
    }
    console.log();

    // 测试6: 为课程添加课程名字段
    console.log('📋 测试6: 为课程集合添加课程名字段');
    try {
      const courseNameField = await client.createField('test_courses', {
        name: 'name',
        type: 'string',
        interface: 'input',
        uiSchema: {
          type: 'string',
          title: '课程名称',
          'x-component': 'Input'
        }
      });
      console.log(`✅ 成功创建课程名字段: ${courseNameField.name}`);
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️  课程名字段已存在，跳过创建');
      } else {
        console.log(`❌ 创建课程名字段失败: ${error.message}`);
      }
    }
    console.log();

    // 测试7: 创建课程记录
    console.log('📋 测试7: 创建课程记录');
    try {
      const course1 = await client.createRecord('test_courses', {
        name: '高等数学'
      });
      console.log(`✅ 成功创建课程记录: ID ${course1.id}, 课程名: ${course1.name}`);

      const course2 = await client.createRecord('test_courses', {
        name: '线性代数'
      });
      console.log(`✅ 成功创建课程记录: ID ${course2.id}, 课程名: ${course2.name}`);
    } catch (error) {
      console.log(`❌ 创建课程记录失败: ${error.message}`);
    }
    console.log();

    // 测试8: 创建学生记录
    console.log('📋 测试8: 创建学生记录');
    try {
      const student1 = await client.createRecord('test_students', {
        name: '王小明'
      });
      console.log(`✅ 成功创建学生记录: ID ${student1.id}, 姓名: ${student1.name}`);

      const student2 = await client.createRecord('test_students', {
        name: '李小红'
      });
      console.log(`✅ 成功创建学生记录: ID ${student2.id}, 姓名: ${student2.name}`);
    } catch (error) {
      console.log(`❌ 创建学生记录失败: ${error.message}`);
    }
    console.log();

    console.log('🎉 MCP工具测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testMCPTools().catch(console.error);
