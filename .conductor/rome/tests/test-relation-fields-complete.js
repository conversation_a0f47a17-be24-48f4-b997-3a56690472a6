#!/usr/bin/env node

/**
 * 完整的关联字段测试 - 学生选课系统
 * 测试所有四种关联类型：hasMany, belongsTo, belongsToMany, hasOne
 */

const { spawn } = require('child_process');
const path = require('path');

// 新的开发环境配置 - 使用正确的API格式
const config = {
  serverUrl: 'https://app.dev.orb.local/api',
  appId: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA'
};

// 学生选课系统测试步骤
const testSteps = [
  // === 第一阶段：创建基础集合 ===
  {
    name: '创建学生集合',
    tool: 'create_collection',
    params: {
      name: 'test_students',
      title: '测试学生',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建课程集合',
    tool: 'create_collection',
    params: {
      name: 'test_courses',
      title: '测试课程',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建教师集合',
    tool: 'create_collection',
    params: {
      name: 'test_teachers',
      title: '测试教师',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建选课记录集合',
    tool: 'create_collection',
    params: {
      name: 'test_enrollments',
      title: '测试选课记录',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建学生档案集合',
    tool: 'create_collection',
    params: {
      name: 'test_profiles',
      title: '测试学生档案',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },

  // === 第二阶段：添加基础字段 ===
  {
    name: '学生-添加姓名字段',
    tool: 'create_field',
    params: {
      collection: 'test_students',
      name: 'name',
      type: 'string',
      interface: 'input',
      description: '学生姓名'
    }
  },
  {
    name: '学生-添加学号字段',
    tool: 'create_field',
    params: {
      collection: 'test_students',
      name: 'student_id',
      type: 'string',
      interface: 'input',
      description: '学号'
    }
  },
  {
    name: '课程-添加课程名字段',
    tool: 'create_field',
    params: {
      collection: 'test_courses',
      name: 'name',
      type: 'string',
      interface: 'input',
      description: '课程名称'
    }
  },
  {
    name: '课程-添加学分字段',
    tool: 'create_field',
    params: {
      collection: 'test_courses',
      name: 'credits',
      type: 'integer',
      interface: 'number',
      description: '学分'
    }
  },
  {
    name: '教师-添加姓名字段',
    tool: 'create_field',
    params: {
      collection: 'test_teachers',
      name: 'name',
      type: 'string',
      interface: 'input',
      description: '教师姓名'
    }
  },
  {
    name: '档案-添加简介字段',
    tool: 'create_field',
    params: {
      collection: 'test_profiles',
      name: 'bio',
      type: 'text',
      interface: 'textarea',
      description: '个人简介'
    }
  },
  {
    name: '选课记录-添加成绩字段',
    tool: 'create_field',
    params: {
      collection: 'test_enrollments',
      name: 'grade',
      type: 'float',
      interface: 'number',
      description: '成绩'
    }
  },

  // === 第三阶段：添加关联字段 ===
  // belongsTo 关系
  {
    name: '课程-添加教师关联(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'test_courses',
      name: 'teacher',
      type: 'belongsTo',
      interface: 'm2o',
      description: '授课教师',
      target: 'test_teachers',
      foreignKey: 'teacherId',
      targetKey: 'id'
    }
  },
  {
    name: '选课记录-添加学生关联(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'test_enrollments',
      name: 'student',
      type: 'belongsTo',
      interface: 'm2o',
      description: '选课学生',
      target: 'test_students',
      foreignKey: 'studentId',
      targetKey: 'id'
    }
  },
  {
    name: '选课记录-添加课程关联(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'test_enrollments',
      name: 'course',
      type: 'belongsTo',
      interface: 'm2o',
      description: '选修课程',
      target: 'test_courses',
      foreignKey: 'courseId',
      targetKey: 'id'
    }
  },
  {
    name: '档案-添加学生关联(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'test_profiles',
      name: 'student',
      type: 'belongsTo',
      interface: 'm2o',
      description: '所属学生',
      target: 'test_students',
      foreignKey: 'studentId',
      targetKey: 'id'
    }
  },

  // hasMany 关系
  {
    name: '学生-添加选课记录关联(hasMany)',
    tool: 'create_field',
    params: {
      collection: 'test_students',
      name: 'enrollments',
      type: 'hasMany',
      interface: 'o2m',
      description: '选课记录',
      target: 'test_enrollments',
      foreignKey: 'studentId',
      sourceKey: 'id'
    }
  },
  {
    name: '课程-添加选课记录关联(hasMany)',
    tool: 'create_field',
    params: {
      collection: 'test_courses',
      name: 'enrollments',
      type: 'hasMany',
      interface: 'o2m',
      description: '选课记录',
      target: 'test_enrollments',
      foreignKey: 'courseId',
      sourceKey: 'id'
    }
  },
  {
    name: '教师-添加课程关联(hasMany)',
    tool: 'create_field',
    params: {
      collection: 'test_teachers',
      name: 'courses',
      type: 'hasMany',
      interface: 'o2m',
      description: '授课课程',
      target: 'test_courses',
      foreignKey: 'teacherId',
      sourceKey: 'id'
    }
  },

  // hasOne 关系
  {
    name: '学生-添加档案关联(hasOne)',
    tool: 'create_field',
    params: {
      collection: 'test_students',
      name: 'profile',
      type: 'hasOne',
      interface: 'oho',
      description: '学生档案',
      target: 'test_profiles',
      foreignKey: 'studentId',
      sourceKey: 'id'
    }
  }
];

// 执行测试
async function runRelationTest() {
  console.log('🚀 开始测试关联字段功能 - 学生选课系统\n');
  console.log(`📍 使用环境: ${config.serverUrl}`);
  console.log(`📱 应用ID: ${config.appId}\n`);
  
  let successCount = 0;
  let failureCount = 0;
  
  for (let i = 0; i < testSteps.length; i++) {
    const step = testSteps[i];
    console.log(`📋 步骤 ${i + 1}/${testSteps.length}: ${step.name}`);
    
    try {
      const result = await callMCPTool(step.tool, step.params);
      
      if (result.result && result.result.isError) {
        console.log(`❌ 失败: ${step.name}`);
        console.log(`   错误详情: ${JSON.stringify(result.result.content, null, 2)}`);
        failureCount++;
        
        // 如果是创建集合失败，可能是已存在，继续执行
        if (step.tool === 'create_collection' && 
            JSON.stringify(result.result.content).includes('already exists')) {
          console.log(`   集合已存在，继续执行...`);
        }
      } else {
        console.log(`✅ 成功: ${step.name}`);
        successCount++;
      }
    } catch (error) {
      console.log(`❌ 失败: ${step.name}`);
      console.log(`   错误: ${error.message}`);
      failureCount++;
    }
    
    console.log('');
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('🎉 测试完成！');
  console.log(`📊 统计: 成功 ${successCount} 个，失败 ${failureCount} 个`);
  console.log('\n📋 现在可以验证关联关系是否正常工作...');
  
  if (successCount > 0) {
    console.log('\n🔍 建议验证步骤:');
    console.log('1. 检查管理界面中的集合是否创建成功');
    console.log('2. 验证关联字段是否正确显示');
    console.log('3. 测试创建记录时关联字段是否可用');
    console.log('4. 验证关联数据的查询和显示');
  }
}

// 调用 MCP 工具
function callMCPTool(tool, params) {
  return new Promise((resolve, reject) => {
    const mcpProcess = spawn('node', [
      path.join(__dirname, 'mcp-server-nocobase/dist/index.js'),
      '--base-url', config.serverUrl,
      '--token', config.token,
      '--app', config.appId
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let error = '';

    mcpProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    mcpProcess.stderr.on('data', (data) => {
      error += data.toString();
    });

    mcpProcess.on('close', (code) => {
      if (code === 0) {
        try {
          // 尝试解析 JSON 响应
          const lines = output.trim().split('\n');
          const lastLine = lines[lines.length - 1];
          if (lastLine.startsWith('{')) {
            const result = JSON.parse(lastLine);
            resolve(result);
          } else {
            resolve({ success: true, output: output.trim() });
          }
        } catch (e) {
          resolve({ success: true, output: output.trim() });
        }
      } else {
        reject(new Error(error || `Process exited with code ${code}`));
      }
    });

    // 发送 MCP 请求
    const request = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/call',
      params: {
        name: tool,
        arguments: params
      }
    };

    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    mcpProcess.stdin.end();
  });
}

// 运行测试
if (require.main === module) {
  runRelationTest().catch(console.error);
}

module.exports = { runRelationTest, testSteps };
