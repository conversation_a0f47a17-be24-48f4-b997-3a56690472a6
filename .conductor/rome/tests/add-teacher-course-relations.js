#!/usr/bin/env node

// 添加教师和课程的关联关系
const { NocoBaseClient } = require('./mcp-server-nocobase/dist/client.js');

async function addTeacherCourseRelations() {
  console.log('🔗 添加教师和课程的关联关系...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 1. 为课程集合添加教师关联字段 (belongsTo)
    console.log('📋 步骤1: 为课程添加教师关联字段 (belongsTo)');
    try {
      const teacherField = await client.createField('test_courses', {
        name: 'teacher',
        type: 'belongsTo',
        interface: 'm2o',
        target: 'test_teachers',
        foreignKey: 'teacherId',
        targetKey: 'id',
        uiSchema: {
          title: '授课教师',
          'x-component': 'AssociationField'
        }
      });
      console.log(`✅ 成功创建课程-教师关联字段: ${teacherField.name}`);
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  课程-教师关联字段已存在');
      } else {
        console.log(`❌ 创建课程-教师关联字段失败: ${error.message}`);
      }
    }
    console.log();

    // 2. 为教师集合添加课程关联字段 (hasMany)
    console.log('📋 步骤2: 为教师添加课程关联字段 (hasMany)');
    try {
      const coursesField = await client.createField('test_teachers', {
        name: 'courses',
        type: 'hasMany',
        interface: 'o2m',
        target: 'test_courses',
        foreignKey: 'teacherId',
        sourceKey: 'id',
        uiSchema: {
          title: '教授课程',
          'x-component': 'AssociationField'
        }
      });
      console.log(`✅ 成功创建教师-课程关联字段: ${coursesField.name}`);
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  教师-课程关联字段已存在');
      } else {
        console.log(`❌ 创建教师-课程关联字段失败: ${error.message}`);
      }
    }
    console.log();

    // 3. 获取现有的教师和课程数据
    console.log('📋 步骤3: 获取现有数据并建立关联');
    const teachers = await client.listRecords('test_teachers');
    const courses = await client.listRecords('test_courses');
    
    console.log(`✅ 找到 ${teachers.data.length} 个教师:`);
    teachers.data.forEach(teacher => {
      console.log(`  • ${teacher.name} (ID: ${teacher.id})`);
    });
    
    console.log(`✅ 找到 ${courses.data.length} 个课程:`);
    courses.data.forEach(course => {
      console.log(`  • ${course.name} (ID: ${course.id})`);
    });
    console.log();

    // 4. 为课程分配教师
    console.log('📋 步骤4: 为课程分配教师');
    if (teachers.data.length > 0 && courses.data.length > 0) {
      // 张教授教授高等数学
      if (courses.data[0]) {
        const teacher1 = teachers.data.find(t => t.name.includes('张')) || teachers.data[0];
        try {
          await client.updateRecord('test_courses', courses.data[0].id, {
            teacherId: teacher1.id
          });
          console.log(`✅ 成功分配: ${teacher1.name} 教授 ${courses.data[0].name}`);
        } catch (error) {
          console.log(`❌ 分配教师失败: ${error.message}`);
        }
      }

      // 李老师教授线性代数
      if (courses.data[1]) {
        const teacher2 = teachers.data.find(t => t.name.includes('李')) || teachers.data[1] || teachers.data[0];
        try {
          await client.updateRecord('test_courses', courses.data[1].id, {
            teacherId: teacher2.id
          });
          console.log(`✅ 成功分配: ${teacher2.name} 教授 ${courses.data[1].name}`);
        } catch (error) {
          console.log(`❌ 分配教师失败: ${error.message}`);
        }
      }
    }
    console.log();

    // 5. 验证关联关系
    console.log('📋 步骤5: 验证教师-课程关联关系');
    
    // 查询教师的课程
    for (const teacher of teachers.data) {
      try {
        const teacherWithCourses = await client.getRecord('test_teachers', teacher.id, ['courses']);
        console.log(`✅ 教师 ${teacher.name}:`);
        if (teacherWithCourses.courses && teacherWithCourses.courses.length > 0) {
          teacherWithCourses.courses.forEach(course => {
            console.log(`   📚 教授课程: ${course.name}`);
          });
        } else {
          console.log(`   📚 暂无课程`);
        }
      } catch (error) {
        console.log(`❌ 查询教师 ${teacher.name} 的课程失败: ${error.message}`);
      }
    }
    console.log();

    // 查询课程的教师
    for (const course of courses.data) {
      try {
        const courseWithTeacher = await client.getRecord('test_courses', course.id, ['teacher']);
        console.log(`✅ 课程 ${course.name}:`);
        if (courseWithTeacher.teacher) {
          console.log(`   👨‍🏫 授课教师: ${courseWithTeacher.teacher.name}`);
        } else {
          console.log(`   👨‍🏫 暂无教师`);
        }
      } catch (error) {
        console.log(`❌ 查询课程 ${course.name} 的教师失败: ${error.message}`);
      }
    }

    console.log('\n🎉 教师-课程关联关系创建完成！');

  } catch (error) {
    console.error('❌ 创建关联关系过程中发生错误:', error.message);
  }
}

addTeacherCourseRelations().catch(console.error);
