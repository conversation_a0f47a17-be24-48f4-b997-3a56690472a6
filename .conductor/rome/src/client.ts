import axios, { type AxiosInstance, type AxiosResponse } from "axios";
import https from "https";

export interface NocoBaseConfig {
  baseUrl: string;
  token: string;
  app: string;
}

export interface Collection {
  key: string;
  name: string;
  title: string;
  inherit: boolean;
  hidden: boolean;
  description?: string;
  autoGenId: boolean;
  createdAt: boolean;
  updatedAt: boolean;
  createdBy: boolean;
  updatedBy: boolean;
  filterTargetKey: string;
  unavailableActions: string[];
  fields?: Field[];
}

export interface Field {
  key: string;
  name: string;
  type: string;
  interface: string;
  collectionName: string;
  description?: string;
  uiSchema?: any;
  [key: string]: any;
}

export interface Record {
  id: number | string;
  [key: string]: any;
}

export interface CollectionCategory {
  id: number;
  name: string;
  color?: string;
  sort?: number;
  collections?: Collection[];
  createdAt?: string;
  updatedAt?: string;
}

export interface User {
  id: number;
  nickname?: string;
  username?: string;
  email?: string;
  phone?: string;
  password?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: any;
  updatedBy?: any;
  [key: string]: any;
}

export interface Role {
  name: string;
  title: string;
  description?: string;
  strategy?: {
    actions: string[];
  };
  default?: boolean;
  hidden?: boolean;
  allowConfigure?: boolean;
  allowNewMenu?: boolean;
  snippets?: string[];
  color?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: any;
  updatedBy?: any;
  [key: string]: any;
}

export interface DesktopRoute {
  id?: number | string;
  parentId?: number | string;
  type: 'page' | 'tab' | 'tabs' | 'group' | 'link';
  title: string;
  icon?: string;
  tooltip?: string;
  schemaUid?: string;
  menuSchemaUid?: string;
  tabSchemaName?: string;
  pageSchemaUid?: string;
  sort?: number;
  options?: {
    href?: string;
    params?: Array<{ name: string; value: any }>;
    openInNewWindow?: boolean;
    [key: string]: any;
  };
  enableTabs?: boolean;
  enableHeader?: boolean;
  displayTitle?: boolean;
  hidden?: boolean;
  hideInMenu?: boolean;
  children?: DesktopRoute[];
  roles?: Array<{ name: string; title: string }>;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: any;
  updatedBy?: any;
  [key: string]: any;
}

export interface ApiResponse<T = any> {
  data: T;
  meta?: {
    count: number;
    page: number;
    pageSize: number;
    totalPage: number;
  };
}

export class NocoBaseClient {
  private client: AxiosInstance;
  private config: NocoBaseConfig;

  constructor(config: NocoBaseConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseUrl,
      headers: {
        "Authorization": `Bearer ${config.token}`,
        "X-App": config.app,
        "Content-Type": "application/json",
        "Accept": "*/*",
        "X-Locale": "en-US",
        "X-Role": "admin",
        "X-Authenticator": "basic",
        "X-Timezone": "+08:00",
        "X-Hostname": "app.dev.orb.local"
      },
      timeout: 30000,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false // 忽略自签名证书
      })
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          const message = error.response.data?.errors?.[0]?.message || error.response.statusText;
          throw new Error(`NocoBase API Error (${error.response.status}): ${message}`);
        } else if (error.request) {
          throw new Error("NocoBase API Error: No response received");
        } else {
          throw new Error(`NocoBase API Error: ${error.message}`);
        }
      }
    );
  }

  // Collections API
  async listCollections(): Promise<Collection[]> {
    const response: AxiosResponse<ApiResponse<Collection[]>> = await this.client.get("/collections:list");
    return response.data.data;
  }

  async listCollectionsMeta(): Promise<Collection[]> {
    const response: AxiosResponse<ApiResponse<Collection[]>> = await this.client.get("/collections:listMeta");
    return response.data.data;
  }

  async getCollection(name: string): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.get(
      `/collections:get?filterByTk=${name}&appends[]=fields`
    );
    return response.data.data;
  }

  async createCollection(collection: Partial<Collection>): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.post(
      "/collections:create",
      collection
    );
    return response.data.data;
  }

  /**
   * Create a collection with proper default fields (id, createdAt, updatedAt, createdBy, updatedBy)
   * This method ensures all default fields are properly defined in the field management system
   * and follows naming best practices
   */
  async createCollectionWithDefaults(params: {
    name: string;
    title?: string;
    description?: string;
    category?: string[];
    autoGenId?: boolean;
    createdAt?: boolean;
    updatedAt?: boolean;
    createdBy?: boolean;
    updatedBy?: boolean;
    fields?: Partial<Field>[];
  }): Promise<Collection> {
    // Prepare default fields
    const defaultFields: any[] = [];

    // ID field
    if (params.autoGenId !== false) {
      defaultFields.push({
        name: 'id',
        type: 'bigInt',
        interface: 'id',
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
        uiSchema: {
          type: 'number',
          title: '{{t("ID")}}',
          'x-component': 'InputNumber',
          'x-read-pretty': true
        }
      });
    }

    // Created at field
    if (params.createdAt !== false) {
      defaultFields.push({
        name: 'createdAt',
        type: 'date',
        interface: 'createdAt',
        field: 'createdAt',
        uiSchema: {
          type: 'datetime',
          title: '{{t("Created at")}}',
          'x-component': 'DatePicker',
          'x-component-props': {},
          'x-read-pretty': true
        }
      });
    }

    // Updated at field
    if (params.updatedAt !== false) {
      defaultFields.push({
        name: 'updatedAt',
        type: 'date',
        interface: 'updatedAt',
        field: 'updatedAt',
        uiSchema: {
          type: 'datetime',
          title: '{{t("Last updated at")}}',
          'x-component': 'DatePicker',
          'x-component-props': {},
          'x-read-pretty': true
        }
      });
    }

    // Created by field
    if (params.createdBy === true) {
      defaultFields.push({
        name: 'createdBy',
        type: 'belongsTo',
        interface: 'createdBy',
        target: 'users',
        foreignKey: 'createdById',
        targetKey: 'id',
        uiSchema: {
          type: 'object',
          title: '{{t("Created by")}}',
          'x-component': 'AssociationField',
          'x-component-props': {
            fieldNames: {
              value: 'id',
              label: 'nickname'
            }
          },
          'x-read-pretty': true
        }
      });
    }

    // Updated by field
    if (params.updatedBy === true) {
      defaultFields.push({
        name: 'updatedBy',
        type: 'belongsTo',
        interface: 'updatedBy',
        target: 'users',
        foreignKey: 'updatedById',
        targetKey: 'id',
        uiSchema: {
          type: 'object',
          title: '{{t("Last updated by")}}',
          'x-component': 'AssociationField',
          'x-component-props': {
            fieldNames: {
              value: 'id',
              label: 'nickname'
            }
          },
          'x-read-pretty': true
        }
      });
    }

    // Merge user fields with default fields
    const allFields = [...defaultFields, ...(params.fields || [])];

    // Prepare collection parameters
    const collectionParams = {
      ...params,
      fields: allFields
    };

    // Filter out undefined values
    const cleanParams = Object.fromEntries(
      Object.entries(collectionParams).filter(([_, value]) => value !== undefined)
    );

    return this.createCollection(cleanParams);
  }

  async updateCollection(name: string, updates: Partial<Collection>): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.post(
      `/collections:update?filterByTk=${name}`,
      updates
    );
    return response.data.data;
  }

  async deleteCollection(name: string): Promise<void> {
    await this.client.post(`/collections:destroy?filterByTk=${name}`);
  }

  // Collection Categories API
  async listCollectionCategories(options?: {
    page?: number;
    pageSize?: number;
    filter?: any;
    sort?: string[];
    appends?: string[];
  }): Promise<{ data: CollectionCategory[]; meta?: any }> {
    const params = new URLSearchParams();

    if (options?.page) params.append("page", options.page.toString());
    if (options?.pageSize) params.append("pageSize", options.pageSize.toString());
    if (options?.filter) params.append("filter", JSON.stringify(options.filter));
    if (options?.sort) {
      options.sort.forEach(s => params.append("sort[]", s));
    }
    if (options?.appends) {
      options.appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<CollectionCategory[]>> = await this.client.get(
      `/collectionCategories:list?${params.toString()}`
    );
    return { data: response.data.data, meta: response.data.meta };
  }

  async getCollectionCategory(id: number | string): Promise<CollectionCategory> {
    const response: AxiosResponse<ApiResponse<CollectionCategory>> = await this.client.get(
      `/collectionCategories:get?filterByTk=${id}&appends[]=collections`
    );
    return response.data.data;
  }

  async createCollectionCategory(category: {
    name: string;
    color?: string;
    sort?: number;
  }): Promise<CollectionCategory> {
    const response: AxiosResponse<ApiResponse<CollectionCategory>> = await this.client.post(
      "/collectionCategories:create",
      category
    );
    return response.data.data;
  }

  async updateCollectionCategory(id: number | string, updates: {
    name?: string;
    color?: string;
    sort?: number;
  }): Promise<CollectionCategory> {
    const response: AxiosResponse<ApiResponse<CollectionCategory>> = await this.client.post(
      `/collectionCategories:update?filterByTk=${id}`,
      updates
    );
    return response.data.data;
  }

  async deleteCollectionCategory(id: number | string): Promise<void> {
    await this.client.post(`/collectionCategories:destroy?filterByTk=${id}`);
  }

  async moveCollectionCategory(options: {
    sourceId: number | string;
    targetId?: number | string;
    targetScope?: string;
    sortField?: string;
    sticky?: boolean;
    method?: string;
  }): Promise<void> {
    await this.client.post("/collectionCategories:move", options);
  }

  // Fields API
  async listFields(collectionName: string): Promise<Field[]> {
    const response: AxiosResponse<ApiResponse<Field[]>> = await this.client.get(
      `/collections/${collectionName}/fields:list`
    );
    return response.data.data;
  }

  async getField(collectionName: string, fieldName: string): Promise<Field> {
    const response: AxiosResponse<ApiResponse<Field>> = await this.client.get(
      `/collections/${collectionName}/fields:get?filterByTk=${fieldName}`
    );
    return response.data.data;
  }

  async createField(collectionName: string, field: Partial<Field>): Promise<Field> {
    const response: AxiosResponse<ApiResponse<Field>> = await this.client.post(
      `/collections/${collectionName}/fields:create`,
      field
    );
    return response.data.data;
  }

  async updateField(collectionName: string, fieldName: string, updates: Partial<Field>): Promise<Field> {
    const response: AxiosResponse<ApiResponse<Field>> = await this.client.post(
      `/collections/${collectionName}/fields:update?filterByTk=${fieldName}`,
      updates
    );
    return response.data.data;
  }

  async deleteField(collectionName: string, fieldName: string): Promise<void> {
    await this.client.post(`/collections/${collectionName}/fields:destroy?filterByTk=${fieldName}`);
  }

  async setCollectionFields(collectionName: string, fields: Partial<Field>[]): Promise<void> {
    await this.client.post(`/collections/${collectionName}/fields:set`, { fields });
  }

  async moveField(collectionName: string, options: {
    sourceId: string | number;
    targetId?: string | number;
    targetScope?: any;
    sortField?: string;
    sticky?: boolean;
    method?: 'insertAfter' | 'prepend';
  }): Promise<void> {
    await this.client.post(`/collections/${collectionName}/fields:move`, options);
  }

  async moveCollection(options: {
    sourceId: string | number;
    targetId?: string | number;
    targetScope?: any;
    sortField?: string;
    sticky?: boolean;
    method?: 'insertAfter' | 'prepend';
  }): Promise<void> {
    await this.client.post("/collections:move", options);
  }

  // Records API
  async listRecords(collectionName: string, options?: {
    page?: number;
    pageSize?: number;
    filter?: any;
    sort?: string[];
    appends?: string[];
  }): Promise<{ data: Record[]; meta?: any }> {
    const params = new URLSearchParams();
    
    if (options?.page) params.append("page", options.page.toString());
    if (options?.pageSize) params.append("pageSize", options.pageSize.toString());
    if (options?.filter) params.append("filter", JSON.stringify(options.filter));
    if (options?.sort) {
      options.sort.forEach(s => params.append("sort[]", s));
    }
    if (options?.appends) {
      options.appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<Record[]>> = await this.client.get(
      `/${collectionName}:list?${params.toString()}`
    );
    return { data: response.data.data, meta: response.data.meta };
  }

  async getRecord(collectionName: string, id: string | number, appends?: string[]): Promise<Record> {
    const params = new URLSearchParams();
    params.append("filterByTk", id.toString());

    if (appends) {
      appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<Record>> = await this.client.get(
      `/${collectionName}:get?${params.toString()}`
    );
    return response.data.data;
  }

  async createRecord(collectionName: string, data: Partial<Record>): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:create`,
      data
    );
    return response.data.data;
  }

  async updateRecord(collectionName: string, id: string | number, data: Partial<Record>): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:update?filterByTk=${id}`,
      data
    );
    return response.data.data;
  }

  async deleteRecord(collectionName: string, id: string | number): Promise<void> {
    await this.client.post(`/${collectionName}:destroy?filterByTk=${id}`);
  }

  // Details Block specific methods
  async getDetailsData(collectionName: string, id: string | number, options?: {
    fields?: string[];
    appends?: string[];
    except?: string[];
  }): Promise<Record> {
    const params = new URLSearchParams();
    params.append("filterByTk", id.toString());

    if (options?.fields) {
      options.fields.forEach(field => params.append("fields[]", field));
    }
    if (options?.appends) {
      options.appends.forEach(append => params.append("appends[]", append));
    }
    if (options?.except) {
      options.except.forEach(except => params.append("except[]", except));
    }

    const response: AxiosResponse<ApiResponse<Record>> = await this.client.get(
      `/${collectionName}:get?${params.toString()}`
    );
    return response.data.data;
  }

  async updateDetailsRecord(collectionName: string, id: string | number, options: {
    values: Partial<Record>;
    overwriteValues?: Partial<Record>;
    assignedValues?: Partial<Record>;
    updateAssociationValues?: boolean;
    triggerWorkflows?: string;
  }): Promise<Record> {
    const { values, overwriteValues = {}, assignedValues = {}, updateAssociationValues, triggerWorkflows } = options;

    const requestData = {
      ...values,
      ...overwriteValues,
      ...assignedValues,
    };

    const params = new URLSearchParams();
    params.append("filterByTk", id.toString());

    if (updateAssociationValues !== undefined) {
      params.append("updateAssociationValues", updateAssociationValues.toString());
    }
    if (triggerWorkflows) {
      params.append("triggerWorkflows", triggerWorkflows);
    }

    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:update?${params.toString()}`,
      requestData
    );
    return response.data.data;
  }

  async deleteDetailsRecord(collectionName: string, id: string | number, options?: {
    triggerWorkflows?: string;
  }): Promise<void> {
    const params = new URLSearchParams();
    params.append("filterByTk", id.toString());

    if (options?.triggerWorkflows) {
      params.append("triggerWorkflows", options.triggerWorkflows);
    }

    await this.client.post(`/${collectionName}:destroy?${params.toString()}`);
  }

  async exportDetailsData(collectionName: string, options?: {
    filterByTk?: string | number;
    filter?: any;
    fields?: string[];
    format?: 'csv' | 'xlsx';
  }): Promise<any> {
    const params = new URLSearchParams();

    if (options?.filterByTk) {
      params.append("filterByTk", options.filterByTk.toString());
    }
    if (options?.filter) {
      params.append("filter", JSON.stringify(options.filter));
    }
    if (options?.fields) {
      options.fields.forEach(field => params.append("fields[]", field));
    }
    if (options?.format) {
      params.append("format", options.format);
    }

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:export?${params.toString()}`
    );
    return response.data.data;
  }

  async bulkUpdateDetailsRecords(collectionName: string, options: {
    values: Partial<Record>;
    filter?: any;
    forceUpdate?: boolean;
  }): Promise<any> {
    const { values, filter, forceUpdate } = options;

    const requestData: any = { values };
    if (filter) requestData.filter = filter;
    if (forceUpdate !== undefined) requestData.forceUpdate = forceUpdate;

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:update`,
      requestData
    );
    return response.data.data;
  }

  async customDetailsRequest(options: {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: { [key: string]: string };
    params?: { [key: string]: any };
    data?: any;
  }): Promise<any> {
    const { url, method, headers, params, data } = options;

    const config: any = {
      method: method.toLowerCase(),
      url,
      headers,
      params,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = data;
    }

    const response = await this.client.request(config);
    return response.data;
  }

  // Routes API
  async listRoutes(options?: { tree?: boolean }): Promise<DesktopRoute[]> {
    const params = options?.tree ? "?tree=true" : "";
    const response: AxiosResponse<ApiResponse<DesktopRoute[]>> = await this.client.get(
      `/desktopRoutes:listAccessible${params}`
    );
    return response.data.data;
  }

  async getRoute(id: string | number): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.get(
      `/desktopRoutes:get?filterByTk=${id}`
    );
    return response.data.data;
  }

  async createRoute(route: Partial<DesktopRoute>): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.post(
      "/desktopRoutes:create",
      route
    );
    return response.data.data;
  }

  async updateRoute(id: string | number, updates: Partial<DesktopRoute>): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.post(
      `/desktopRoutes:update?filterByTk=${id}`,
      updates
    );
    return response.data.data;
  }

  async deleteRoute(id: string | number): Promise<void> {
    await this.client.post(`/desktopRoutes:destroy?filterByTk=${id}`);
  }

  async moveRoute(options: {
    sourceId: string | number;
    targetId?: string | number;
    targetScope?: any;
    sortField?: string;
    sticky?: boolean;
    method?: 'insertAfter' | 'prepend';
  }): Promise<void> {
    await this.client.post("/desktopRoutes:move", options);
  }

  // UI Schema API
  async createPageSchema(schemaUid: string, schema: any): Promise<any> {
    // 确保 schema 包含正确的 x-uid
    const schemaWithUid = {
      ...schema,
      'x-uid': schemaUid
    };

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      "/uiSchemas:insert",
      schemaWithUid
    );
    return response.data.data;
  }

  async getPageSchema(schemaUid: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/uiSchemas:getJsonSchema/${schemaUid}`
    );
    return response.data.data;
  }

  async insertBlockSchema(parentUid: string, blockSchema: any, position?: string): Promise<any> {
    // 对于页面级别的插入，我们需要插入到页面的 grid 属性中
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      "/uiSchemas:insertAdjacent",
      {
        parentUid,
        schema: blockSchema,
        position: position || 'beforeEnd',
        wrap: null // 确保不包装
      }
    );
    return response.data.data;
  }

  async insertBlockToGrid(parentUid: string, blockSchema: any, position?: string): Promise<any> {
    // 专门用于向页面的 Grid 中插入区块的方法
    // 使用正确的 API 端点格式，基于 Playwright 观察到的成功模式
    const positionParam = position || 'beforeEnd';
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:insertAdjacent/${parentUid}?position=${positionParam}`,
      {
        schema: blockSchema
      }
    );
    return response.data.data;
  }

  async insertMenuSchema(parentMenuUid: string, menuSchema: any, position?: string): Promise<any> {
    // 插入菜单项到指定的父菜单中
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:insertAdjacent/${parentMenuUid}?position=${position || 'beforeEnd'}`,
      {
        schema: menuSchema
      }
    );
    return response.data.data;
  }

  async updateBlockSchema(blockUid: string, updates: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:patch`,
      {
        'x-uid': blockUid,
        ...updates
      }
    );
    return response.data.data;
  }

  async deleteBlockSchema(blockUid: string): Promise<void> {
    await this.client.post(`/uiSchemas:remove`, {
      'x-uid': blockUid
    });
  }

  async getSchemaProperties(schemaUid: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/uiSchemas:getProperties/${schemaUid}`
    );
    return response.data.data;
  }

  async getSchema(schemaUid: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/uiSchemas:getJsonSchema/${schemaUid}`
    );
    return response.data.data;
  }

  async insertSchema(parentUid: string, schema: any, position?: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:insertAdjacent/${parentUid}?position=${position || 'beforeEnd'}`,
      {
        schema: schema
      }
    );
    return response.data.data;
  }

  // Users API
  async listUsers(options?: {
    page?: number;
    pageSize?: number;
    filter?: any;
    sort?: string[];
    appends?: string[];
  }): Promise<{ data: User[]; meta?: any }> {
    const params = new URLSearchParams();

    if (options?.page) params.append("page", options.page.toString());
    if (options?.pageSize) params.append("pageSize", options.pageSize.toString());
    if (options?.filter) params.append("filter", JSON.stringify(options.filter));
    if (options?.sort) {
      options.sort.forEach(s => params.append("sort[]", s));
    }
    if (options?.appends) {
      options.appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<User[]>> = await this.client.get(
      `/users:list?${params.toString()}`
    );
    return { data: response.data.data, meta: response.data.meta };
  }

  async getUser(id: string | number, appends?: string[]): Promise<User> {
    const params = new URLSearchParams();
    params.append("filterByTk", id.toString());

    if (appends) {
      appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<User>> = await this.client.get(
      `/users:get?${params.toString()}`
    );
    return response.data.data;
  }

  async createUser(data: Partial<User>): Promise<User> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.post(
      "/users:create",
      data
    );
    return response.data.data;
  }

  async updateUser(id: string | number, data: Partial<User>): Promise<User> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.post(
      `/users:update?filterByTk=${id}`,
      data
    );
    return response.data.data;
  }

  async deleteUser(id: string | number): Promise<void> {
    await this.client.post(`/users:destroy?filterByTk=${id}`);
  }

  // Roles API
  async listRoles(options?: {
    page?: number;
    pageSize?: number;
    filter?: any;
    sort?: string[];
    appends?: string[];
  }): Promise<{ data: Role[]; meta?: any }> {
    const params = new URLSearchParams();

    if (options?.page) params.append("page", options.page.toString());
    if (options?.pageSize) params.append("pageSize", options.pageSize.toString());
    if (options?.filter) params.append("filter", JSON.stringify(options.filter));
    if (options?.sort) {
      options.sort.forEach(s => params.append("sort[]", s));
    }
    if (options?.appends) {
      options.appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<Role[]>> = await this.client.get(
      `/roles:list?${params.toString()}`
    );
    return { data: response.data.data, meta: response.data.meta };
  }

  async getRole(name: string, appends?: string[]): Promise<Role> {
    const params = new URLSearchParams();
    params.append("filterByTk", name);

    if (appends) {
      appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<Role>> = await this.client.get(
      `/roles:get?${params.toString()}`
    );
    return response.data.data;
  }

  async createRole(data: Partial<Role>): Promise<Role> {
    const response: AxiosResponse<ApiResponse<Role>> = await this.client.post(
      "/roles:create",
      data
    );
    return response.data.data;
  }

  async updateRole(name: string, data: Partial<Role>): Promise<Role> {
    const response: AxiosResponse<ApiResponse<Role>> = await this.client.post(
      `/roles:update?filterByTk=${name}`,
      data
    );
    return response.data.data;
  }

  async deleteRole(name: string): Promise<void> {
    await this.client.post(`/roles:destroy?filterByTk=${name}`);
  }

  async checkRole(): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get("/roles:check");
    return response.data.data;
  }

  async setDefaultRole(name: string): Promise<void> {
    await this.client.post("/roles:setDefaultRole", { name });
  }

  // Extended UI Schema API for table operations
  async insertAdjacentSchema(targetUid: string, schema: any, position: 'beforeBegin' | 'afterBegin' | 'beforeEnd' | 'afterEnd' = 'beforeEnd', wrap?: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:insertAdjacent/${targetUid}?position=${position}`,
      {
        schema,
        wrap
      }
    );
    return response.data.data;
  }

  async patchSchema(uid: string, updates: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:patch`,
      {
        'x-uid': uid,
        ...updates
      }
    );
    return response.data.data;
  }

  async removeSchema(uid: string): Promise<void> {
    await this.client.post(`/uiSchemas:remove/${uid}`);
  }

  async getSchemaJsonSchema(uid: string, includeAsyncNode?: boolean): Promise<any> {
    const params = includeAsyncNode ? '?includeAsyncNode=true' : '';
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/uiSchemas:getJsonSchema/${uid}${params}`
    );
    return response.data.data;
  }

  async sendCustomRequest(requestId: string, data: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/customRequests:send/${requestId}`,
      data
    );
    return response.data.data;
  }

  // Table-specific operations
  async addTableAction(tableUid: string, actionConfig: {
    title: string;
    action: string;
    icon?: string;
    type?: string;
    align?: 'left' | 'right';
    requiresACL?: boolean;
    aclAction?: string;
    settings?: string;
    componentProps?: any;
    position?: 'beforeBegin' | 'afterBegin' | 'beforeEnd' | 'afterEnd';
  }): Promise<any> {
    const actionSchema = {
      type: 'void',
      title: actionConfig.title,
      'x-component': 'Action',
      'x-action': actionConfig.action,
      'x-designer': 'Action.Designer',
      'x-component-props': {
        ...(actionConfig.icon && { icon: actionConfig.icon }),
        ...(actionConfig.type && { type: actionConfig.type }),
        ...actionConfig.componentProps
      },
      ...(actionConfig.requiresACL && {
        'x-decorator': 'ACLActionProvider',
        'x-acl-action': actionConfig.aclAction
      }),
      ...(actionConfig.settings && { 'x-settings': actionConfig.settings }),
      ...(actionConfig.align && { 'x-align': actionConfig.align })
    };

    return await this.insertAdjacentSchema(
      tableUid,
      actionSchema,
      actionConfig.position || 'beforeEnd'
    );
  }

  async removeTableAction(actionUid: string): Promise<void> {
    await this.removeSchema(actionUid);
  }

  async updateTableAction(actionUid: string, updates: any): Promise<any> {
    return await this.patchSchema(actionUid, updates);
  }

  async getTableActions(tableUid: string): Promise<any> {
    return await this.getSchemaProperties(tableUid);
  }

  // Enhanced record operations with advanced parameters
  async createRecordAdvanced(collectionName: string, options: {
    values: any;
    whitelist?: string[];
    blacklist?: string[];
    updateAssociationValues?: boolean;
  }): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:create`,
      options
    );
    return response.data.data;
  }

  async updateRecordAdvanced(collectionName: string, id: string | number, options: {
    values: any;
    whitelist?: string[];
    blacklist?: string[];
    updateAssociationValues?: boolean;
    forceUpdate?: boolean;
    filter?: any;
  }): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:update?filterByTk=${id}`,
      options
    );
    return response.data.data;
  }

  async getRecordAdvanced(collectionName: string, id: string | number, options?: {
    fields?: string[];
    appends?: string[];
    except?: string[];
    filter?: any;
  }): Promise<Record> {
    const params = new URLSearchParams();
    params.append("filterByTk", id.toString());

    if (options?.fields) {
      options.fields.forEach(f => params.append("fields[]", f));
    }
    if (options?.appends) {
      options.appends.forEach(a => params.append("appends[]", a));
    }
    if (options?.except) {
      options.except.forEach(e => params.append("except[]", e));
    }
    if (options?.filter) {
      params.append("filter", JSON.stringify(options.filter));
    }

    const response: AxiosResponse<ApiResponse<Record>> = await this.client.get(
      `/${collectionName}:get?${params.toString()}`
    );
    return response.data.data;
  }

  async deleteRecordAdvanced(collectionName: string, id: string | number, options?: {
    filter?: any;
  }): Promise<void> {
    const data: any = {};
    if (options?.filter) {
      data.filter = options.filter;
    }

    await this.client.post(`/${collectionName}:destroy?filterByTk=${id}`, data);
  }

  // Association operations
  async addAssociation(collectionName: string, sourceId: string | number, associationField: string, options: {
    filterByTk?: string | number;
    filterByTks?: any[];
    values?: any;
  }): Promise<void> {
    await this.client.post(
      `/${collectionName}/${sourceId}/${associationField}:add`,
      options
    );
  }

  async removeAssociation(collectionName: string, sourceId: string | number, associationField: string, options: {
    filterByTk?: string | number;
    filterByTks?: any[];
    values?: any;
  }): Promise<void> {
    await this.client.post(
      `/${collectionName}/${sourceId}/${associationField}:remove`,
      options
    );
  }

  async setAssociation(collectionName: string, sourceId: string | number, associationField: string, options: {
    filterByTk?: string | number;
    filterByTks?: any[];
    values?: any;
  }): Promise<void> {
    await this.client.post(
      `/${collectionName}/${sourceId}/${associationField}:set`,
      options
    );
  }

  async toggleAssociation(collectionName: string, sourceId: string | number, associationField: string, options: {
    values: any;
  }): Promise<void> {
    await this.client.post(
      `/${collectionName}/${sourceId}/${associationField}:toggle`,
      options
    );
  }

  // Advanced operations
  async firstOrCreate(collectionName: string, options: {
    values: any;
    filter?: any;
  }): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:firstOrCreate`,
      options
    );
    return response.data.data;
  }

  async updateOrCreate(collectionName: string, options: {
    values: any;
    filter?: any;
  }): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:updateOrCreate`,
      options
    );
    return response.data.data;
  }

  // List Block Operations API

  /**
   * 获取列表数据
   */
  async getListData(collectionName: string, options?: {
    page?: number;
    pageSize?: number;
    filter?: any;
    fields?: string[];
    appends?: string[];
    except?: string[];
    sort?: string[];
    tree?: boolean;
  }): Promise<any> {
    const params = new URLSearchParams();

    if (options?.page) params.append('page', options.page.toString());
    if (options?.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options?.filter) params.append('filter', JSON.stringify(options.filter));
    if (options?.fields) params.append('fields', options.fields.join(','));
    if (options?.appends) params.append('appends', options.appends.join(','));
    if (options?.except) params.append('except', options.except.join(','));
    if (options?.sort) params.append('sort', options.sort.join(','));
    if (options?.tree) params.append('tree', 'true');

    const response = await this.client.get(`/${collectionName}:list?${params.toString()}`);
    return response.data;
  }

  /**
   * 创建列表项
   */
  async createListItem(collectionName: string, values: any, options?: {
    filterKeys?: string[];
    updateAssociationValues?: boolean;
    triggerWorkflows?: string;
    overwriteValues?: any;
    assignedValues?: any;
  }): Promise<any> {
    const data: any = { values };

    if (options?.filterKeys) data.filterKeys = options.filterKeys;
    if (options?.updateAssociationValues) data.updateAssociationValues = options.updateAssociationValues;
    if (options?.triggerWorkflows) data.triggerWorkflows = options.triggerWorkflows;
    if (options?.overwriteValues) data.overwriteValues = options.overwriteValues;
    if (options?.assignedValues) data.assignedValues = options.assignedValues;

    const response = await this.client.post(`/${collectionName}:create`, data);
    return response.data.data;
  }

  /**
   * 更新列表项
   */
  async updateListItem(collectionName: string, itemId: string | number, values: any, options?: {
    overwriteValues?: any;
    assignedValues?: any;
    updateAssociationValues?: boolean;
    triggerWorkflows?: string;
  }): Promise<any> {
    const data: any = { values };

    if (options?.overwriteValues) data.overwriteValues = options.overwriteValues;
    if (options?.assignedValues) data.assignedValues = options.assignedValues;
    if (options?.updateAssociationValues) data.updateAssociationValues = options.updateAssociationValues;
    if (options?.triggerWorkflows) data.triggerWorkflows = options.triggerWorkflows;

    const response = await this.client.post(`/${collectionName}:update?filterByTk=${itemId}`, data);
    return response.data.data;
  }

  /**
   * 删除列表项
   */
  async deleteListItem(collectionName: string, itemId: string | number, options?: {
    filter?: any;
    triggerWorkflows?: string;
  }): Promise<void> {
    const data: any = {};

    if (options?.filter) data.filter = options.filter;
    if (options?.triggerWorkflows) data.triggerWorkflows = options.triggerWorkflows;

    await this.client.post(`/${collectionName}:destroy?filterByTk=${itemId}`, data);
  }

  /**
   * 批量删除列表项
   */
  async bulkDeleteListItems(collectionName: string, itemIds: (string | number)[], options?: {
    filter?: any;
    triggerWorkflows?: string;
  }): Promise<void> {
    const data: any = { filterByTk: itemIds };

    if (options?.filter) data.filter = options.filter;
    if (options?.triggerWorkflows) data.triggerWorkflows = options.triggerWorkflows;

    await this.client.post(`/${collectionName}:destroy`, data);
  }

  /**
   * 查看列表项
   */
  async viewListItem(collectionName: string, itemId: string | number, options?: {
    filter?: any;
    fields?: string[];
    appends?: string[];
    except?: string[];
  }): Promise<any> {
    const params = new URLSearchParams();

    if (options?.filter) params.append('filter', JSON.stringify(options.filter));
    if (options?.fields) params.append('fields', options.fields.join(','));
    if (options?.appends) params.append('appends', options.appends.join(','));
    if (options?.except) params.append('except', options.except.join(','));

    const response = await this.client.get(`/${collectionName}:get?filterByTk=${itemId}&${params.toString()}`);
    return response.data.data;
  }

  /**
   * 刷新列表
   */
  async refreshList(listUid: string): Promise<void> {
    // 这个方法通常在前端处理，这里提供一个占位实现
    // 实际使用中可能需要根据具体的 NocoBase 版本调整
    await this.client.post(`/uiSchemas:refresh`, { uid: listUid });
  }

  /**
   * 筛选列表
   */
  async filterList(listUid: string, options: {
    filter: any;
    page?: number;
    pageSize?: number;
  }): Promise<any> {
    const data = {
      uid: listUid,
      filter: options.filter,
      page: options.page || 1,
      pageSize: options.pageSize || 10
    };

    const response = await this.client.post(`/uiSchemas:filter`, data);
    return response.data;
  }

  /**
   * 添加列表操作按钮
   */
  async addListAction(listUid: string, actionConfig: any, position?: string): Promise<any> {
    const actionUid = `action_${Date.now()}`;
    const actionSchema = {
      type: 'void',
      'x-uid': actionUid,
      title: actionConfig.title,
      'x-action': actionConfig.action,
      'x-component': 'Action',
      'x-component-props': {
        icon: actionConfig.icon,
        type: actionConfig.type,
        ...actionConfig.componentProps
      },
      'x-align': actionConfig.align,
      'x-use-component-props': actionConfig.useComponentProps,
      ...(actionConfig.requiresACL && { 'x-acl-action': actionConfig.aclAction }),
      ...(actionConfig.settings && { 'x-settings': actionConfig.settings })
    };

    const response = await this.client.post(`/uiSchemas:insertAdjacent`, {
      targetUid: listUid,
      position: position || 'beforeEnd',
      schema: actionSchema
    });

    return response.data;
  }

  /**
   * 配置列表项操作按钮
   */
  async configureListItemActions(listUid: string, actionConfigs: any[]): Promise<any> {
    const itemActionBarSchema: any = {
      type: 'void',
      'x-component': 'ActionBar',
      'x-initializer': 'list:configureItemActions',
      'x-use-component-props': 'useListActionBarProps',
      'x-component-props': {
        layout: 'one-column'
      },
      properties: {}
    };

    // 为每个操作创建 schema
    actionConfigs.forEach((config, index) => {
      const actionUid = `item_action_${Date.now()}_${index}`;
      itemActionBarSchema.properties[actionUid] = {
        type: 'void',
        'x-uid': actionUid,
        title: config.title,
        'x-action': config.action,
        'x-component': config.component || 'Action.Link',
        'x-component-props': {
          icon: config.icon,
          ...config.componentProps,
          ...(config.confirm && { confirm: config.confirm })
        },
        'x-use-component-props': config.useComponentProps,
        ...(config.requiresACL && { 'x-acl-action': config.aclAction }),
        ...(config.settings && { 'x-settings': config.settings })
      };
    });

    const response = await this.client.post(`/uiSchemas:patch`, {
      uid: listUid,
      schema: {
        properties: {
          list: {
            properties: {
              item: {
                properties: {
                  actionBar: itemActionBarSchema
                }
              }
            }
          }
        }
      }
    });

    return response.data;
  }

  /**
   * 配置列表字段
   */
  async configureListFields(listUid: string, fields: any[]): Promise<any> {
    const fieldsSchema: any = {};

    fields.forEach((field, index) => {
      const fieldUid = `field_${Date.now()}_${index}`;
      fieldsSchema[fieldUid] = {
        type: 'string',
        'x-uid': fieldUid,
        title: field.title || field.name,
        'x-decorator': 'FormItem',
        'x-component': field.component || 'Input.ReadPretty',
        'x-component-props': {
          span: field.span || 12,
          ...field.componentProps
        },
        'x-read-pretty': true,
        'x-collection-field': field.name,
        required: field.required || false
      };
    });

    const response = await this.client.post(`/uiSchemas:patch`, {
      uid: listUid,
      schema: {
        properties: {
          list: {
            properties: {
              item: {
                properties: {
                  grid: {
                    type: 'void',
                    'x-component': 'Grid',
                    'x-initializer': 'details:configureFields',
                    properties: fieldsSchema
                  }
                }
              }
            }
          }
        }
      }
    });

    return response.data;
  }

  /**
   * 导出列表数据
   */
  async exportListData(collectionName: string, options?: {
    filter?: any;
    fields?: string[];
    format?: string;
    pageSize?: number;
    page?: number;
  }): Promise<any> {
    const data: any = {};

    if (options?.filter) data.filter = options.filter;
    if (options?.fields) data.fields = options.fields;
    if (options?.format) data.format = options.format;
    if (options?.pageSize) data.pageSize = options.pageSize;
    if (options?.page) data.page = options.page;

    const response = await this.client.post(`/${collectionName}:export`, data);
    return response.data;
  }

  /**
   * 导入列表数据
   */
  async importListData(collectionName: string, file: string, options?: {
    fields?: string[];
    updateStrategy?: string;
  }): Promise<any> {
    const data: any = { file };

    if (options?.fields) data.fields = options.fields;
    if (options?.updateStrategy) data.updateStrategy = options.updateStrategy;

    const response = await this.client.post(`/${collectionName}:importXlsx`, data);
    return response.data;
  }

  /**
   * 自定义列表请求
   */
  async customListRequest(options: {
    url: string;
    method?: string;
    headers?: any;
    params?: any;
    data?: any;
  }): Promise<any> {
    const config: any = {
      url: options.url,
      method: options.method || 'GET'
    };

    if (options.headers) config.headers = options.headers;
    if (options.params) config.params = options.params;
    if (options.data) config.data = options.data;

    const response = await this.client.request(config);
    return response.data;
  }

  // Kanban API
  /**
   * 获取看板数据
   */
  async getKanbanData(collectionName: string, options?: {
    groupField?: string;
    sortField?: string;
    filter?: any;
    appends?: string[];
    fields?: string[];
  }): Promise<any> {
    const params: any = {
      paginate: false,
      ...options
    };

    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/${collectionName}:list`,
      { params }
    );
    return response.data.data;
  }

  /**
   * 创建看板卡片
   */
  async createKanbanCard(collectionName: string, cardData: any, groupField?: string, columnValue?: any): Promise<any> {
    const values = { ...cardData };
    if (groupField && columnValue !== undefined) {
      values[groupField] = columnValue;
    }

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:create`,
      {
        values,
        whitelist: Object.keys(values),
        updateAssociationValues: true,
      }
    );
    return response.data.data;
  }

  /**
   * 更新看板卡片
   */
  async updateKanbanCard(collectionName: string, cardId: string | number, updateData: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:update`,
      {
        filterByTk: cardId,
        values: updateData,
        whitelist: Object.keys(updateData),
        updateAssociationValues: true,
      }
    );
    return response.data.data;
  }

  /**
   * 删除看板卡片
   */
  async deleteKanbanCard(collectionName: string, cardId: string | number): Promise<void> {
    await this.client.post(`/${collectionName}:destroy`, {
      filterByTk: cardId,
    });
  }

  /**
   * 移动看板卡片
   */
  async moveKanbanCard(collectionName: string, options: {
    sourceId: string | number;
    targetId?: string | number;
    targetScope?: any;
    sortField?: string;
    sticky?: boolean;
    method?: string;
  }): Promise<void> {
    await this.client.post(`/${collectionName}:move`, options);
  }

  /**
   * 批量更新看板卡片状态
   */
  async batchUpdateKanbanCards(collectionName: string, cardIds: (string | number)[], updateData: any): Promise<any[]> {
    const results = [];
    for (const cardId of cardIds) {
      const result = await this.updateKanbanCard(collectionName, cardId, updateData);
      results.push(result);
    }
    return results;
  }

  /**
   * 创建排序字段
   */
  async createSortField(collectionName: string, fieldData: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/collections/${collectionName}/fields:create`,
      {
        values: {
          type: 'sort',
          interface: 'sort',
          ...fieldData,
        },
      }
    );
    return response.data.data;
  }

  /**
   * 更新排序字段（第三方数据源）
   */
  async updateSortField(dataSource: string, collectionName: string, fieldName: string, fieldData: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/dataSourcesCollections/${dataSource}.${collectionName}/fields:update?filterByTk=${fieldName}`,
      {
        type: 'sort',
        interface: 'sort',
        ...fieldData
      }
    );
    return response.data.data;
  }

  // Grid Card API
  /**
   * 获取网格卡片数据
   */
  async getGridCardData(collectionName: string, options?: {
    page?: number;
    pageSize?: number;
    filter?: any;
    fields?: string[];
    appends?: string[];
    sort?: string[];
    except?: string[];
  }): Promise<{ data: any[], meta: any }> {
    const params: any = {
      page: options?.page || 1,
      pageSize: options?.pageSize || 12, // Grid Card 默认分页大小
      ...options
    };

    const response: AxiosResponse<ApiResponse<any[]>> = await this.client.get(
      `/${collectionName}:list`,
      { params }
    );
    return { data: response.data.data, meta: response.data.meta };
  }

  /**
   * 创建网格卡片项
   */
  async createGridCardItem(collectionName: string, values: any, options?: {
    whitelist?: string[];
    blacklist?: string[];
    updateAssociationValues?: boolean;
    triggerWorkflows?: string;
  }): Promise<any> {
    const requestData: any = { values };

    if (options?.whitelist) requestData.whitelist = options.whitelist;
    if (options?.blacklist) requestData.blacklist = options.blacklist;
    if (options?.updateAssociationValues) requestData.updateAssociationValues = options.updateAssociationValues;
    if (options?.triggerWorkflows) requestData.triggerWorkflows = options.triggerWorkflows;

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:create`,
      requestData
    );
    return response.data.data;
  }

  /**
   * 更新网格卡片项
   */
  async updateGridCardItem(collectionName: string, itemId: string | number, values: any, options?: {
    whitelist?: string[];
    blacklist?: string[];
    updateAssociationValues?: boolean;
    forceUpdate?: boolean;
    triggerWorkflows?: string;
  }): Promise<any> {
    const requestData: any = {
      filterByTk: itemId,
      values
    };

    if (options?.whitelist) requestData.whitelist = options.whitelist;
    if (options?.blacklist) requestData.blacklist = options.blacklist;
    if (options?.updateAssociationValues) requestData.updateAssociationValues = options.updateAssociationValues;
    if (options?.forceUpdate) requestData.forceUpdate = options.forceUpdate;
    if (options?.triggerWorkflows) requestData.triggerWorkflows = options.triggerWorkflows;

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:update`,
      requestData
    );
    return response.data.data;
  }

  /**
   * 删除网格卡片项
   */
  async deleteGridCardItem(collectionName: string, itemId: string | number, options?: {
    filter?: any;
    triggerWorkflows?: string;
  }): Promise<void> {
    const requestData: any = {
      filterByTk: itemId
    };

    if (options?.filter) requestData.filter = options.filter;
    if (options?.triggerWorkflows) requestData.triggerWorkflows = options.triggerWorkflows;

    await this.client.post(`/${collectionName}:destroy`, requestData);
  }

  /**
   * 查看网格卡片项
   */
  async viewGridCardItem(collectionName: string, itemId: string | number, options?: {
    fields?: string[];
    appends?: string[];
    except?: string[];
  }): Promise<any> {
    const params = new URLSearchParams();
    params.append('filterByTk', itemId.toString());

    if (options?.fields) options.fields.forEach(f => params.append('fields[]', f));
    if (options?.appends) options.appends.forEach(a => params.append('appends[]', a));
    if (options?.except) options.except.forEach(e => params.append('except[]', e));

    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/${collectionName}:get?${params.toString()}`
    );
    return response.data.data;
  }

  /**
   * 导出网格卡片数据
   */
  async exportGridCardData(collectionName: string, options?: {
    filter?: any;
    fields?: string[];
    format?: 'xlsx' | 'csv';
  }): Promise<any> {
    const requestData: any = {};

    if (options?.filter) requestData.filter = options.filter;
    if (options?.fields) requestData.fields = options.fields;

    const endpoint = options?.format === 'csv' ? 'exportCsv' : 'exportXlsx';
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:${endpoint}`,
      requestData
    );
    return response.data.data;
  }

  /**
   * 导入网格卡片数据
   */
  async importGridCardData(collectionName: string, file: any, options?: {
    explain?: boolean;
    dryRun?: boolean;
  }): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    if (options?.explain) formData.append('explain', 'true');
    if (options?.dryRun) formData.append('dryRun', 'true');

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:importXlsx`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      }
    );
    return response.data.data;
  }

  /**
   * 添加网格卡片操作按钮
   */
  async addGridCardAction(gridCardUid: string, actionConfig: {
    title: string;
    action: string;
    icon?: string;
    type?: string;
    align?: 'left' | 'right';
    requiresACL?: boolean;
    aclAction?: string;
    settings?: string;
    position?: 'beforeBegin' | 'afterBegin' | 'beforeEnd' | 'afterEnd';
  }): Promise<any> {
    const actionUid = `action_${Date.now()}`;
    const actionSchema: any = {
      type: 'void',
      'x-uid': actionUid,
      title: actionConfig.title,
      'x-action': actionConfig.action,
      'x-component': 'Action',
      'x-use-component-props': 'useGridCardActionProps',
      'x-component-props': {
        icon: actionConfig.icon,
        type: actionConfig.type,
      },
      'x-align': actionConfig.align || 'right',
      'x-settings': actionConfig.settings,
    };

    if (actionConfig.requiresACL) {
      actionSchema['x-decorator'] = 'ACLActionProvider';
      actionSchema['x-acl-action'] = actionConfig.aclAction || actionConfig.action;
      actionSchema['x-acl-action-props'] = { skipScopeCheck: true };
    }

    return await this.insertAdjacentSchema(
      gridCardUid,
      actionSchema,
      actionConfig.position || 'beforeEnd'
    );
  }

  /**
   * 配置网格卡片项操作按钮
   */
  async configureGridCardItemActions(gridCardUid: string, actionConfigs: any[]): Promise<any> {
    const itemActionBarSchema: any = {
      type: 'void',
      'x-component': 'ActionBar',
      'x-initializer': 'gridCard:configureItemActions',
      'x-use-component-props': 'useGridCardActionBarProps',
      'x-component-props': {
        layout: 'one-column',
      },
      properties: {}
    };

    // 添加每个操作
    actionConfigs.forEach((config: any, index: number) => {
      const actionUid = `action_${Date.now()}_${index}`;
      itemActionBarSchema.properties[`action_${index}`] = {
        type: 'void',
        'x-uid': actionUid,
        title: config.title,
        'x-action': config.action,
        'x-component': config.component || 'Action.Link',
        'x-align': config.align || 'left',
        'x-use-component-props': config.useComponentProps || 'useGridCardItemActionProps',
        'x-component-props': config.componentProps || {},
      };

      if (config.requiresACL) {
        itemActionBarSchema.properties[`action_${index}`]['x-decorator'] = 'ACLActionProvider';
        itemActionBarSchema.properties[`action_${index}`]['x-acl-action'] = config.aclAction || config.action;
      }

      if (config.settings) {
        itemActionBarSchema.properties[`action_${index}`]['x-settings'] = config.settings;
      }
    });

    return await this.insertAdjacentSchema(
      gridCardUid,
      itemActionBarSchema,
      'beforeEnd'
    );
  }

  // Calendar API
  /**
   * 获取日历数据
   */
  async getCalendarData(collectionName: string, options?: {
    filter?: any;
    fields?: string[];
    appends?: string[];
    dateRange?: {
      start: string;
      end: string;
    };
    fieldNames?: {
      id?: string;
      title?: string;
      start?: string;
      end?: string;
      colorFieldName?: string;
    };
  }): Promise<any[]> {
    const params: any = {
      paginate: false, // Calendar 默认不分页
      ...options
    };

    // 如果指定了日期范围，添加到筛选条件中
    if (options?.dateRange && options?.fieldNames?.start) {
      const dateFilter = {
        $and: [
          {
            [options.fieldNames.start]: {
              $gte: options.dateRange.start
            }
          },
          {
            [options.fieldNames.start]: {
              $lte: options.dateRange.end
            }
          }
        ]
      };

      params.filter = params.filter
        ? { $and: [params.filter, dateFilter] }
        : dateFilter;
    }

    const response: AxiosResponse<ApiResponse<any[]>> = await this.client.get(
      `/${collectionName}:list`,
      { params }
    );
    return response.data.data;
  }

  /**
   * 创建日历事件
   */
  async createCalendarEvent(collectionName: string, eventData: any, options?: {
    whitelist?: string[];
    updateAssociationValues?: boolean;
    triggerWorkflows?: string;
  }): Promise<any> {
    const requestData: any = {
      values: eventData,
      whitelist: options?.whitelist || ['title', 'description', 'startDate', 'endDate', 'priority'],
      updateAssociationValues: options?.updateAssociationValues ?? true,
    };

    if (options?.triggerWorkflows) {
      requestData.triggerWorkflows = options.triggerWorkflows;
    }

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:create`,
      requestData
    );
    return response.data.data;
  }

  /**
   * 更新日历事件
   */
  async updateCalendarEvent(collectionName: string, eventId: string | number, updateData: any, options?: {
    whitelist?: string[];
    updateAssociationValues?: boolean;
    triggerWorkflows?: string;
  }): Promise<any> {
    const requestData: any = {
      filterByTk: eventId,
      values: updateData,
      whitelist: options?.whitelist || ['title', 'description', 'startDate', 'endDate', 'priority'],
      updateAssociationValues: options?.updateAssociationValues ?? true,
    };

    if (options?.triggerWorkflows) {
      requestData.triggerWorkflows = options.triggerWorkflows;
    }

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/${collectionName}:update`,
      requestData
    );
    return response.data.data;
  }

  /**
   * 删除日历事件
   */
  async deleteCalendarEvent(collectionName: string, eventId: string | number, options?: {
    triggerWorkflows?: string;
  }): Promise<void> {
    const requestData: any = {
      filterByTk: eventId,
    };

    if (options?.triggerWorkflows) {
      requestData.triggerWorkflows = options.triggerWorkflows;
    }

    await this.client.post(`/${collectionName}:destroy`, requestData);
  }

  /**
   * 移动日历事件（更新时间）
   */
  async moveCalendarEvent(collectionName: string, eventId: string | number, newTimes: {
    start: string;
    end?: string;
  }, fieldNames?: {
    start?: string;
    end?: string;
  }): Promise<any> {
    const startField = fieldNames?.start || 'startDate';
    const endField = fieldNames?.end || 'endDate';

    const values: any = {
      [startField]: newTimes.start,
    };

    if (newTimes.end) {
      values[endField] = newTimes.end;
    }

    return await this.updateCalendarEvent(collectionName, eventId, values, {
      whitelist: [startField, endField]
    });
  }

  /**
   * 处理重复事件删除
   */
  async deleteRecurringEvent(collectionName: string, eventId: string | number, deleteOption: 'all' | string): Promise<any> {
    if (deleteOption === 'all') {
      // 删除所有重复事件
      return await this.deleteCalendarEvent(collectionName, eventId);
    } else {
      // 更新 exclude 数组，排除特定日期
      const currentEvent = await this.getRecord(collectionName, eventId, {
        fields: ['exclude']
      });

      const currentExclude = currentEvent.exclude || [];
      const newExclude = [...currentExclude, deleteOption];

      return await this.updateCalendarEvent(collectionName, eventId, {
        exclude: newExclude
      }, {
        whitelist: ['exclude']
      });
    }
  }

  /**
   * 获取日历事件详情
   */
  async getCalendarEvent(collectionName: string, eventId: string | number, options?: {
    fields?: string[];
    appends?: string[];
    except?: string[];
  }): Promise<any> {
    const params = new URLSearchParams();
    params.append('filterByTk', eventId.toString());

    if (options?.fields) options.fields.forEach(f => params.append('fields[]', f));
    if (options?.appends) options.appends.forEach(a => params.append('appends[]', a));
    if (options?.except) options.except.forEach(e => params.append('except[]', e));

    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/${collectionName}:get?${params.toString()}`
    );
    return response.data.data;
  }

  /**
   * 批量创建日历事件
   */
  async batchCreateCalendarEvents(collectionName: string, eventsData: any[], options?: {
    whitelist?: string[];
    updateAssociationValues?: boolean;
    triggerWorkflows?: string;
  }): Promise<any[]> {
    const results = [];
    for (const eventData of eventsData) {
      const result = await this.createCalendarEvent(collectionName, eventData, options);
      results.push(result);
    }
    return results;
  }

  /**
   * 批量更新日历事件
   */
  async batchUpdateCalendarEvents(collectionName: string, updates: Array<{
    id: string | number;
    values: any;
  }>, options?: {
    whitelist?: string[];
    updateAssociationValues?: boolean;
    triggerWorkflows?: string;
  }): Promise<any[]> {
    const results = [];
    for (const update of updates) {
      const result = await this.updateCalendarEvent(collectionName, update.id, update.values, options);
      results.push(result);
    }
    return results;
  }

  /**
   * 添加日历操作按钮
   */
  async addCalendarAction(calendarUid: string, actionConfig: {
    name: string;
    title: string;
    component: string;
    action?: string;
    align?: 'left' | 'right';
    icon?: string;
    requiresACL?: boolean;
    aclAction?: string;
    settings?: string;
    position?: 'beforeBegin' | 'afterBegin' | 'beforeEnd' | 'afterEnd';
  }): Promise<any> {
    const actionUid = `calendar_action_${Date.now()}`;
    const actionSchema: any = {
      type: 'void',
      'x-uid': actionUid,
      title: actionConfig.title,
      'x-component': actionConfig.component,
      'x-align': actionConfig.align || 'left',
    };

    if (actionConfig.action) {
      actionSchema['x-action'] = actionConfig.action;
    }

    if (actionConfig.icon) {
      actionSchema['x-component-props'] = {
        icon: actionConfig.icon,
      };
    }

    if (actionConfig.requiresACL) {
      actionSchema['x-decorator'] = 'ACLActionProvider';
      actionSchema['x-acl-action'] = actionConfig.aclAction || actionConfig.action;
      actionSchema['x-acl-action-props'] = { skipScopeCheck: true };
    }

    if (actionConfig.settings) {
      actionSchema['x-settings'] = actionConfig.settings;
    }

    return await this.insertAdjacentSchema(
      calendarUid,
      actionSchema,
      actionConfig.position || 'beforeEnd'
    );
  }

  /**
   * 配置日历字段映射
   */
  async configureCalendarFields(calendarUid: string, fieldNames: {
    id?: string;
    title: string;
    start: string;
    end?: string;
    colorFieldName?: string;
  }): Promise<any> {
    // 更新日历块的字段映射配置
    const updateData = {
      'x-decorator-props': {
        fieldNames: {
          id: 'id',
          ...fieldNames,
        }
      }
    };

    return await this.updateBlockSchema(calendarUid, updateData);
  }

  /**
   * 更新日历块设置
   */
  async updateCalendarSettings(calendarUid: string, settings: {
    showLunar?: boolean;
    defaultView?: 'month' | 'week' | 'day';
    enableQuickCreateEvent?: boolean;
    weekStart?: number;
  }): Promise<any> {
    const updateData = {
      'x-decorator-props': settings
    };

    return await this.updateBlockSchema(calendarUid, updateData);
  }

  // Markdown API
  /**
   * 获取Markdown区块内容
   */
  async getMarkdownBlockContent(blockUid: string): Promise<{
    content: string;
    templateEngine: string | null;
  }> {
    const blockSchema = await this.getBlockSchema(blockUid);
    return {
      content: blockSchema?.['x-component-props']?.content || '',
      templateEngine: blockSchema?.['x-decorator-props']?.engine || null
    };
  }

  /**
   * 更新Markdown区块内容
   */
  async updateMarkdownBlockContent(blockUid: string, content: string, templateEngine?: string | null): Promise<any> {
    const updateData: any = {
      'x-component-props': {
        content
      }
    };

    if (templateEngine !== undefined) {
      updateData['x-decorator-props'] = {
        engine: templateEngine
      };
    }

    return await this.updateBlockSchema(blockUid, updateData);
  }

  /**
   * 设置Markdown区块模板引擎
   */
  async setMarkdownTemplateEngine(blockUid: string, engine: 'handlebars' | null): Promise<any> {
    const updateData = {
      'x-decorator-props': {
        engine
      }
    };

    return await this.updateBlockSchema(blockUid, updateData);
  }

  /**
   * 创建带变量的Markdown内容
   */
  async createMarkdownWithVariables(blockUid: string, template: string, variables?: Array<{
    name: string;
    path: string;
    description?: string;
  }>): Promise<any> {
    let content = template;

    // 如果提供了变量信息，在内容前添加变量说明
    if (variables && variables.length > 0) {
      const variableDoc = variables.map(v =>
        `- **${v.name}**: \`{{${v.path}}}\`${v.description ? ` - ${v.description}` : ''}`
      ).join('\n');
      content = `<!-- Variables used in this template:\n${variableDoc}\n-->\n\n${template}`;
    }

    const updateData = {
      'x-component-props': {
        content
      },
      'x-decorator-props': {
        engine: 'handlebars' // 启用模板引擎支持变量
      }
    };

    return await this.updateBlockSchema(blockUid, updateData);
  }

  // Menu API
  /**
   * 创建菜单路由
   */
  async createMenuRoute(routeData: {
    title: string;
    type: 'group' | 'page' | 'link' | 'url';
    icon?: string;
    tooltip?: string;
    parentId?: number | null;
    schemaUid?: string;
    options?: any;
    sort?: number;
    hideInMenu?: boolean;
    enableTabs?: boolean;
    enableHeader?: boolean;
  }): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      '/desktopRoutes:create',
      { values: routeData }
    );
    return response.data.data;
  }

  /**
   * 更新菜单路由
   */
  async updateMenuRoute(routeId: string | number, updateData: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      '/desktopRoutes:update',
      {
        filterByTk: routeId,
        values: updateData
      }
    );
    return response.data.data;
  }

  /**
   * 删除菜单路由
   */
  async deleteMenuRoute(routeId: string | number): Promise<void> {
    await this.client.post('/desktopRoutes:destroy', {
      filterByTk: routeId
    });
  }

  /**
   * 移动菜单路由
   */
  async moveMenuRoute(params: {
    sourceId: string | number;
    targetId?: string | number;
    method?: 'insertAfter' | 'prepend' | 'append';
    sortField?: string;
  }): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      '/desktopRoutes:move',
      params
    );
    return response.data.data;
  }

  /**
   * 获取可访问的菜单路由
   */
  async getAccessibleMenuRoutes(params?: {
    tree?: boolean;
    filter?: any;
    sort?: string;
  }): Promise<any[]> {
    const response: AxiosResponse<ApiResponse<any[]>> = await this.client.get(
      '/desktopRoutes:listAccessible',
      { params }
    );
    return response.data.data;
  }

  /**
   * 设置角色菜单权限
   */
  async setRoleMenuPermissions(roleId: string | number, menuRouteIds: (string | number)[]): Promise<void> {
    await this.client.post('/roles.desktopRoutes:set', {
      resourceName: 'desktopRoutes',
      sourceId: roleId,
      values: menuRouteIds
    });
  }

  /**
   * 创建菜单分组
   */
  async createMenuGroup(params: {
    title: string;
    icon?: string;
    parentId?: number | null;
    insertPosition?: string;
    targetId?: string | number;
  }): Promise<any> {
    const { title, icon, parentId, insertPosition, targetId } = params;

    // 创建分组路由
    const routeData = {
      type: 'group' as const,
      title,
      icon,
      parentId: insertPosition === 'beforeEnd' ? parentId : undefined
    };

    const result = await this.createMenuRoute(routeData);

    // 如果需要移动到特定位置
    if (insertPosition && insertPosition !== 'beforeEnd' && targetId) {
      const method = insertPosition === 'beforeBegin' || insertPosition === 'afterBegin'
        ? 'insertAfter' as const
        : 'prepend' as const;

      await this.moveMenuRoute({
        sourceId: result.id,
        targetId,
        method,
        sortField: 'sort'
      });
    }

    return result;
  }

  /**
   * 创建菜单页面
   */
  async createMenuPage(params: {
    title: string;
    icon?: string;
    parentId?: number | null;
    schemaUid: string;
    insertPosition?: string;
    targetId?: string | number;
    enableTabs?: boolean;
    enableHeader?: boolean;
  }): Promise<any> {
    const {
      title,
      icon,
      parentId,
      schemaUid,
      insertPosition,
      targetId,
      enableTabs = false,
      enableHeader = true
    } = params;

    // 创建页面路由
    const routeData = {
      type: 'page' as const,
      title,
      icon,
      schemaUid,
      parentId: insertPosition === 'beforeEnd' ? parentId : undefined,
      enableTabs,
      enableHeader
    };

    const result = await this.createMenuRoute(routeData);

    // 如果需要移动到特定位置
    if (insertPosition && insertPosition !== 'beforeEnd' && targetId) {
      const method = insertPosition === 'beforeBegin' || insertPosition === 'afterBegin'
        ? 'insertAfter' as const
        : 'prepend' as const;

      await this.moveMenuRoute({
        sourceId: result.id,
        targetId,
        method,
        sortField: 'sort'
      });
    }

    return result;
  }

  /**
   * 创建菜单链接
   */
  async createMenuLink(params: {
    title: string;
    icon?: string;
    href: string;
    target?: string;
    parentId?: number | null;
    insertPosition?: string;
    targetId?: string | number;
  }): Promise<any> {
    const {
      title,
      icon,
      href,
      target = '_self',
      parentId,
      insertPosition,
      targetId
    } = params;

    // 创建链接路由
    const routeData = {
      type: 'link' as const,
      title,
      icon,
      parentId: insertPosition === 'beforeEnd' ? parentId : undefined,
      options: {
        href,
        target
      }
    };

    const result = await this.createMenuRoute(routeData);

    // 如果需要移动到特定位置
    if (insertPosition && insertPosition !== 'beforeEnd' && targetId) {
      const method = insertPosition === 'beforeBegin' || insertPosition === 'afterBegin'
        ? 'insertAfter' as const
        : 'prepend' as const;

      await this.moveMenuRoute({
        sourceId: result.id,
        targetId,
        method,
        sortField: 'sort'
      });
    }

    return result;
  }

  // Filter API
  /**
   * 添加筛选表单区块
   */
  async addFilterFormBlock(params: {
    parentUid: string;
    collectionName: string;
    title?: string;
    filterTargets?: Array<{ uid: string; field?: string }>;
    fields?: string[];
    position?: string;
  }): Promise<any> {
    const { parentUid, collectionName, title = 'Filter', filterTargets = [], fields = [], position = 'beforeEnd' } = params;

    // 创建筛选表单区块的Schema
    const filterBlockSchema = this.createFilterFormBlockSchema({
      collectionName,
      title,
      filterTargets,
      fields
    });

    return await this.insertAdjacentSchema(parentUid, filterBlockSchema, position);
  }

  /**
   * 添加关联筛选区块
   */
  async addAssociationFilterBlock(params: {
    parentUid: string;
    collectionName: string;
    associationField: string;
    title?: string;
    filterTargets?: Array<{ uid: string; field?: string }>;
    position?: string;
  }): Promise<any> {
    const { parentUid, collectionName, associationField, title, filterTargets = [], position = 'beforeEnd' } = params;

    // 创建关联筛选区块的Schema
    const filterBlockSchema = this.createAssociationFilterBlockSchema({
      collectionName,
      associationField,
      title,
      filterTargets
    });

    return await this.insertAdjacentSchema(parentUid, filterBlockSchema, position);
  }

  /**
   * 配置筛选目标
   */
  async configureFilterTargets(filterBlockUid: string, targets: Array<{ uid: string; field?: string }>): Promise<any> {
    const updateData = {
      'x-filter-targets': targets
    };

    return await this.updateBlockSchema(filterBlockUid, updateData);
  }

  /**
   * 添加筛选字段
   */
  async addFilterField(params: {
    filterBlockUid: string;
    fieldName: string;
    operator?: string;
    title?: string;
    required?: boolean;
    position?: string;
  }): Promise<any> {
    const { filterBlockUid, fieldName, operator = '$eq', title, required = false, position = 'beforeEnd' } = params;

    // 创建筛选字段的Schema
    const fieldSchema = this.createFilterFieldSchema({
      fieldName,
      operator,
      title,
      required
    });

    // 将字段添加到筛选表单的Grid中
    const gridUid = await this.findFilterFormGrid(filterBlockUid);
    return await this.insertAdjacentSchema(gridUid, fieldSchema, position);
  }

  /**
   * 执行筛选操作
   */
  async executeFilter(params: {
    filterBlockUid: string;
    filterValues: any;
    operators?: any;
  }): Promise<any> {
    // 注意：这个方法主要是模拟筛选执行，实际的筛选逻辑在前端进行
    // 这里我们可以返回筛选条件的转换结果
    const { filterBlockUid, filterValues, operators = {} } = params;

    const transformedFilter = this.transformToFilter(filterValues, operators);

    return {
      filterBlockUid,
      transformedFilter,
      affectedBlocks: 1 // 模拟影响的区块数量
    };
  }

  /**
   * 重置筛选操作
   */
  async resetFilter(params: {
    filterBlockUid: string;
    clearDefaultValue?: boolean;
  }): Promise<any> {
    // 注意：这个方法主要是模拟重置操作，实际的重置逻辑在前端进行
    const { filterBlockUid, clearDefaultValue = false } = params;

    return {
      filterBlockUid,
      clearDefaultValue,
      affectedBlocks: 1 // 模拟影响的区块数量
    };
  }

  /**
   * 获取筛选条件
   */
  async getFilterConditions(filterBlockUid: string): Promise<any> {
    const blockSchema = await this.getBlockSchema(filterBlockUid);

    // 从Schema中提取筛选相关信息
    const filterTargets = blockSchema?.['x-filter-targets'] || [];
    const defaultValues = blockSchema?.['x-component-props']?.defaultValues || {};

    return {
      filterBlockUid,
      targets: filterTargets,
      values: defaultValues,
      operators: {} // 可以从Schema中提取操作符信息
    };
  }

  /**
   * 设置筛选默认值
   */
  async setFilterDefaultValues(filterBlockUid: string, defaultValues: any): Promise<any> {
    const updateData = {
      'x-component-props': {
        defaultValues
      }
    };

    return await this.updateBlockSchema(filterBlockUid, updateData);
  }

  /**
   * 创建筛选表单区块Schema
   */
  private createFilterFormBlockSchema(params: {
    collectionName: string;
    title: string;
    filterTargets: Array<{ uid: string; field?: string }>;
    fields: string[];
  }): any {
    const { collectionName, title, filterTargets, fields } = params;
    const blockUid = `filter_form_${Date.now()}`;
    const gridUid = `grid_${Date.now()}`;

    return {
      type: 'void',
      'x-uid': blockUid,
      'x-decorator': 'FilterFormBlockProvider',
      'x-decorator-props': {
        collection: collectionName,
        dataSource: 'main'
      },
      'x-filter-targets': filterTargets,
      'x-component': 'CardItem',
      'x-component-props': {
        title
      },
      properties: {
        form: {
          type: 'void',
          'x-component': 'FormV2',
          'x-use-component-props': 'useFilterFormBlockProps',
          properties: {
            grid: {
              type: 'void',
              'x-uid': gridUid,
              'x-component': 'Grid',
              'x-initializer': 'filterForm:configureFields',
              properties: this.createFilterFieldsProperties(fields)
            },
            actions: {
              type: 'void',
              'x-component': 'ActionBar',
              'x-component-props': {
                layout: 'one-column'
              },
              properties: {
                filter: {
                  type: 'void',
                  title: '{{ t("Filter") }}',
                  'x-action': 'filter',
                  'x-component': 'Action',
                  'x-use-component-props': 'useFilterBlockActionProps',
                  'x-component-props': {
                    icon: 'SearchOutlined',
                    htmlType: 'submit',
                    type: 'primary'
                  }
                },
                reset: {
                  type: 'void',
                  title: '{{ t("Reset") }}',
                  'x-action': 'reset',
                  'x-component': 'Action',
                  'x-use-component-props': 'useResetBlockActionProps',
                  'x-component-props': {
                    icon: 'ReloadOutlined'
                  }
                }
              }
            }
          }
        }
      }
    };
  }

  /**
   * 创建关联筛选区块Schema
   */
  private createAssociationFilterBlockSchema(params: {
    collectionName: string;
    associationField: string;
    title?: string;
    filterTargets: Array<{ uid: string; field?: string }>;
  }): any {
    const { collectionName, associationField, title, filterTargets } = params;
    const blockUid = `association_filter_${Date.now()}`;

    return {
      type: 'void',
      'x-uid': blockUid,
      'x-decorator': 'AssociationFilterProvider',
      'x-decorator-props': {
        collection: collectionName,
        associationField,
        dataSource: 'main'
      },
      'x-filter-targets': filterTargets,
      'x-component': 'CardItem',
      'x-component-props': {
        title: title || `${associationField} Filter`
      },
      properties: {
        filter: {
          type: 'void',
          'x-component': 'AssociationFilter',
          'x-use-component-props': 'useAssociationFilterProps'
        }
      }
    };
  }

  /**
   * 创建筛选字段Schema
   */
  private createFilterFieldSchema(params: {
    fieldName: string;
    operator: string;
    title?: string;
    required: boolean;
  }): any {
    const { fieldName, operator, title, required } = params;
    const fieldUid = `filter_field_${fieldName}_${Date.now()}`;

    return {
      type: 'string',
      'x-uid': fieldUid,
      'x-collection-field': fieldName,
      'x-component': 'CollectionField',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        required
      },
      'x-component-props': {
        operator,
        title: title || undefined
      }
    };
  }

  /**
   * 创建筛选字段属性
   */
  private createFilterFieldsProperties(fields: string[]): any {
    const properties: any = {};

    fields.forEach((fieldName, index) => {
      const fieldUid = `filter_field_${fieldName}_${Date.now()}_${index}`;
      properties[`field_${index}`] = {
        type: 'string',
        'x-uid': fieldUid,
        'x-collection-field': fieldName,
        'x-component': 'CollectionField',
        'x-decorator': 'FormItem'
      };
    });

    return properties;
  }

  /**
   * 查找筛选表单的Grid UID
   */
  private async findFilterFormGrid(filterBlockUid: string): Promise<string> {
    const blockSchema = await this.getBlockSchema(filterBlockUid);
    // 简化实现，实际应该遍历Schema树查找Grid组件
    return blockSchema?.properties?.form?.properties?.grid?.['x-uid'] || `grid_${Date.now()}`;
  }

  /**
   * 转换表单值为筛选条件
   */
  private transformToFilter(values: any, operators: any): any {
    const conditions: any = {};

    Object.keys(values).forEach(key => {
      const value = values[key];
      const operator = operators[key] || '$eq';

      if (value !== null && value !== undefined && value !== '') {
        conditions[key] = {
          [operator]: value
        };
      }
    });

    return Object.keys(conditions).length > 0 ? { $and: [conditions] } : {};
  }
}
