# Calendar Operations Tools 测试文档

## 概述

本文档提供了 NocoBase Calendar Operations 工具的测试用例和使用示例。

## 测试环境

- **NocoBase 测试环境**: https://n.astra.xin/apps/mcp_playground
- **API Base URL**: https://n.astra.xin/api
- **App ID**: mcp_playground
- **测试集合**: events (假设已创建包含日历事件的集合)

## 1. 获取日历数据测试

### 基础获取
```json
{
  "tool": "get_calendar_data",
  "arguments": {
    "collectionName": "events"
  }
}
```

### 带日期范围筛选
```json
{
  "tool": "get_calendar_data",
  "arguments": {
    "collectionName": "events",
    "dateRange": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-12-31T23:59:59.999Z"
    },
    "fieldNames": {
      "title": "title",
      "start": "startDate",
      "end": "endDate",
      "colorFieldName": "priority"
    }
  }
}
```

### 带筛选条件
```json
{
  "tool": "get_calendar_data",
  "arguments": {
    "collectionName": "events",
    "filter": {
      "priority": "high"
    },
    "fields": ["id", "title", "startDate", "endDate", "priority"],
    "appends": ["assignee"]
  }
}
```

## 2. 创建日历事件测试

### 基础事件创建
```json
{
  "tool": "create_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventData": {
      "title": "团队会议",
      "description": "每周例会讨论项目进展",
      "startDate": "2024-01-15T09:00:00.000Z",
      "endDate": "2024-01-15T10:00:00.000Z",
      "priority": "high"
    }
  }
}
```

### 重复事件创建
```json
{
  "tool": "create_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventData": {
      "title": "每周站会",
      "description": "团队每周站会",
      "startDate": "2024-01-15T09:00:00.000Z",
      "endDate": "2024-01-15T09:30:00.000Z",
      "cron": "every_week",
      "priority": "medium"
    },
    "whitelist": ["title", "description", "startDate", "endDate", "cron", "priority"]
  }
}
```

## 3. 更新日历事件测试

### 基础更新
```json
{
  "tool": "update_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 1,
    "updateData": {
      "title": "团队会议（已更新）",
      "description": "更新后的会议描述",
      "priority": "medium"
    }
  }
}
```

### 时间调整
```json
{
  "tool": "update_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 1,
    "updateData": {
      "startDate": "2024-01-15T10:00:00.000Z",
      "endDate": "2024-01-15T11:00:00.000Z"
    },
    "whitelist": ["startDate", "endDate"]
  }
}
```

## 4. 移动日历事件测试

### 移动事件到新时间
```json
{
  "tool": "move_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 1,
    "newTimes": {
      "start": "2024-01-16T09:00:00.000Z",
      "end": "2024-01-16T10:00:00.000Z"
    }
  }
}
```

### 使用自定义字段名
```json
{
  "tool": "move_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 1,
    "newTimes": {
      "start": "2024-01-16T09:00:00.000Z",
      "end": "2024-01-16T10:00:00.000Z"
    },
    "fieldNames": {
      "start": "startTime",
      "end": "endTime"
    }
  }
}
```

## 5. 删除日历事件测试

### 删除单个事件
```json
{
  "tool": "delete_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 1
  }
}
```

### 删除重复事件（所有）
```json
{
  "tool": "delete_recurring_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 2,
    "deleteOption": "all"
  }
}
```

### 删除重复事件（特定日期）
```json
{
  "tool": "delete_recurring_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 2,
    "deleteOption": "2024-01-22T09:00:00.000Z"
  }
}
```

## 6. 批量操作测试

### 批量创建事件
```json
{
  "tool": "batch_create_calendar_events",
  "arguments": {
    "collectionName": "events",
    "eventsData": [
      {
        "title": "项目启动会",
        "startDate": "2024-01-20T09:00:00.000Z",
        "endDate": "2024-01-20T10:00:00.000Z",
        "priority": "high"
      },
      {
        "title": "设计评审",
        "startDate": "2024-01-21T14:00:00.000Z",
        "endDate": "2024-01-21T15:30:00.000Z",
        "priority": "medium"
      }
    ]
  }
}
```

### 批量更新事件
```json
{
  "tool": "batch_update_calendar_events",
  "arguments": {
    "collectionName": "events",
    "updates": [
      {
        "id": 1,
        "values": { "priority": "low" }
      },
      {
        "id": 2,
        "values": { "priority": "high" }
      }
    ]
  }
}
```

## 7. 日历配置测试

### 配置字段映射
```json
{
  "tool": "configure_calendar_fields",
  "arguments": {
    "calendarUid": "calendar_block_uid_here",
    "fieldNames": {
      "title": "eventTitle",
      "start": "startTime",
      "end": "endTime",
      "colorFieldName": "category"
    }
  }
}
```

### 更新日历设置
```json
{
  "tool": "update_calendar_settings",
  "arguments": {
    "calendarUid": "calendar_block_uid_here",
    "settings": {
      "showLunar": true,
      "defaultView": "week",
      "enableQuickCreateEvent": true,
      "weekStart": 1
    }
  }
}
```

### 添加日历操作按钮
```json
{
  "tool": "add_calendar_action",
  "arguments": {
    "calendarUid": "calendar_block_uid_here",
    "actionConfig": {
      "name": "export",
      "title": "导出日历",
      "component": "Action",
      "action": "export",
      "align": "right",
      "icon": "ExportOutlined",
      "requiresACL": true,
      "aclAction": "events:export"
    }
  }
}
```

## 8. 筛选和查询测试

### 复杂筛选条件
```json
{
  "tool": "filter_calendar_events",
  "arguments": {
    "collectionName": "events",
    "filterConditions": {
      "$and": [
        { "priority": { "$in": ["high", "medium"] } },
        { "assignee": { "$notNull": true } }
      ]
    },
    "dateRange": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-01-31T23:59:59.999Z"
    },
    "sort": ["startDate"]
  }
}
```

### 获取特定事件详情
```json
{
  "tool": "get_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 1,
    "fields": ["id", "title", "description", "startDate", "endDate", "priority"],
    "appends": ["assignee", "attendees"]
  }
}
```

## 9. 错误处理测试

### 无效事件ID
```json
{
  "tool": "get_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 99999
  }
}
```

### 无效集合名称
```json
{
  "tool": "get_calendar_data",
  "arguments": {
    "collectionName": "non_existent_collection"
  }
}
```

## 10. 性能测试

### 大量数据获取
```json
{
  "tool": "get_calendar_data",
  "arguments": {
    "collectionName": "events",
    "dateRange": {
      "start": "2020-01-01T00:00:00.000Z",
      "end": "2025-12-31T23:59:59.999Z"
    },
    "fields": ["id", "title", "startDate", "endDate"]
  }
}
```

## 预期结果

所有测试用例都应该：
1. 返回正确的JSON格式响应
2. 包含适当的成功或错误消息
3. 遵循NocoBase API的响应格式
4. 正确处理权限和ACL检查
5. 支持重复事件的特殊逻辑

## 注意事项

1. 确保测试集合 `events` 已创建并包含适当的字段
2. 测试前需要获取正确的 `calendarUid` 和 `parentUid`
3. 重复事件测试需要包含 `cron` 和 `exclude` 字段
4. 权限测试需要使用具有适当权限的用户账号
