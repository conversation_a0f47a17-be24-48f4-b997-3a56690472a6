/**
 * NocoBase Grid Card Operations Tools
 * 提供网格卡片区块的操作功能，包括数据获取、CRUD操作、筛选、导入导出等
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';
import { uid } from '../utils.js';

/**
 * 预定义的网格卡片操作配置
 */
export const GRID_CARD_ACTION_TEMPLATES = {
  // 块级别操作
  filter: {
    title: "{{t('Filter')}}",
    action: 'filter',
    icon: 'FilterOutlined',
    align: 'left' as const,
    requiresACL: false,
    settings: 'actionSettings:filter'
  },
  
  addNew: {
    title: "{{t('Add new')}}",
    action: 'create',
    icon: 'PlusOutlined',
    type: 'primary',
    align: 'right' as const,
    requiresACL: true,
    aclAction: 'create',
    settings: 'actionSettings:addNew'
  },
  
  refresh: {
    title: "{{t('Refresh')}}",
    action: 'refresh',
    icon: 'ReloadOutlined',
    align: 'right' as const,
    requiresACL: false,
    settings: 'actionSettings:refresh'
  },
  
  import: {
    title: "{{t('Import')}}",
    action: 'importXlsx',
    icon: 'ImportOutlined',
    align: 'right' as const,
    requiresACL: true,
    aclAction: 'importXlsx',
    settings: 'actionSettings:import'
  },
  
  export: {
    title: "{{t('Export')}}",
    action: 'exportXlsx',
    icon: 'ExportOutlined',
    align: 'right' as const,
    requiresACL: true,
    aclAction: 'export',
    settings: 'actionSettings:export'
  },
  
  customRequest: {
    title: "{{t('Custom request')}}",
    action: 'customize:table:request:global',
    icon: 'ApiOutlined',
    align: 'right' as const,
    requiresACL: false,
    settings: 'actionSettings:customRequest'
  }
};

/**
 * 预定义的网格卡片项操作配置
 */
export const GRID_CARD_ITEM_ACTION_TEMPLATES = {
  view: {
    title: "{{t('View')}}",
    action: 'view',
    component: 'Action.Link',
    align: 'left' as const,
    requiresACL: true,
    aclAction: 'get'
  },
  
  edit: {
    title: "{{t('Edit')}}",
    action: 'update',
    component: 'Action.Link',
    align: 'left' as const,
    requiresACL: true,
    aclAction: 'update'
  },
  
  delete: {
    title: "{{t('Delete')}}",
    action: 'destroy',
    component: 'Action.Link',
    align: 'left' as const,
    requiresACL: true,
    aclAction: 'destroy'
  },
  
  popup: {
    title: "{{t('Popup')}}",
    action: 'popup',
    component: 'Action.Link',
    align: 'left' as const,
    requiresACL: false
  },
  
  updateRecord: {
    title: "{{t('Update record')}}",
    action: 'update',
    component: 'Action',
    align: 'left' as const,
    requiresACL: true,
    aclAction: 'update'
  },
  
  customRequest: {
    title: "{{t('Custom request')}}",
    action: 'customize:table:request',
    component: 'Action.Link',
    align: 'left' as const,
    requiresACL: false
  },
  
  link: {
    title: "{{t('Link')}}",
    action: 'link',
    component: 'Action.Link',
    align: 'left' as const,
    requiresACL: false
  }
};

/**
 * 获取网格卡片数据工具
 */
export const getGridCardDataTool: Tool = {
  name: 'get_grid_card_data',
  description: 'Get grid card data with pagination, filtering, and sorting',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      page: {
        type: 'number',
        description: 'Page number (default: 1)'
      },
      pageSize: {
        type: 'number',
        description: 'Page size (default: 12 for grid cards)'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions for the grid card data'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in the response'
      },
      appends: {
        type: 'array',
        items: { type: 'string' },
        description: 'Associated fields to include'
      },
      sort: {
        type: 'array',
        items: { type: 'string' },
        description: 'Sort fields (e.g., ["-createdAt", "name"])'
      },
      except: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to exclude from the response'
      }
    },
    required: ['collectionName']
  }
};

export async function handleGetGridCardData(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, page, pageSize, filter, fields, appends, sort, except } = args;
    
    const data = await client.getGridCardData(collectionName, {
      page,
      pageSize,
      filter,
      fields,
      appends,
      sort,
      except
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card data retrieved successfully:\n${JSON.stringify({
            collection: collectionName,
            totalItems: data.meta?.count || 0,
            currentPage: data.meta?.page || 1,
            pageSize: data.meta?.pageSize || 12,
            totalPages: data.meta?.totalPage || 1,
            itemsCount: data.data?.length || 0
          }, null, 2)}\n\nData:\n${JSON.stringify(data.data, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving grid card data: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 创建网格卡片项工具
 */
export const createGridCardItemTool: Tool = {
  name: 'create_grid_card_item',
  description: 'Create a new grid card item',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      values: {
        type: 'object',
        description: 'The data for the new grid card item'
      },
      whitelist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields allowed to be set'
      },
      blacklist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields not allowed to be set'
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values'
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflow trigger configuration'
      }
    },
    required: ['collectionName', 'values']
  }
};

export async function handleCreateGridCardItem(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, values, whitelist, blacklist, updateAssociationValues, triggerWorkflows } = args;
    
    const result = await client.createGridCardItem(collectionName, values, {
      whitelist,
      blacklist,
      updateAssociationValues,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card item created successfully:\n${JSON.stringify({
            collection: collectionName,
            id: result.id,
            data: result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating grid card item: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 更新网格卡片项工具
 */
export const updateGridCardItemTool: Tool = {
  name: 'update_grid_card_item',
  description: 'Update a grid card item',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      itemId: {
        type: ['string', 'number'],
        description: 'The ID of the item to update'
      },
      values: {
        type: 'object',
        description: 'The updated data for the grid card item'
      },
      whitelist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields allowed to be updated'
      },
      blacklist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields not allowed to be updated'
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values'
      },
      forceUpdate: {
        type: 'boolean',
        description: 'Whether to force update'
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflow trigger configuration'
      }
    },
    required: ['collectionName', 'itemId', 'values']
  }
};

export async function handleUpdateGridCardItem(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, itemId, values, whitelist, blacklist, updateAssociationValues, forceUpdate, triggerWorkflows } = args;

    const result = await client.updateGridCardItem(collectionName, itemId, values, {
      whitelist,
      blacklist,
      updateAssociationValues,
      forceUpdate,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card item updated successfully:\n${JSON.stringify({
            collection: collectionName,
            id: itemId,
            data: result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating grid card item: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 删除网格卡片项工具
 */
export const deleteGridCardItemTool: Tool = {
  name: 'delete_grid_card_item',
  description: 'Delete a grid card item',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      itemId: {
        type: ['string', 'number'],
        description: 'The ID of the item to delete'
      },
      filter: {
        type: 'object',
        description: 'Additional filter conditions'
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflow trigger configuration'
      }
    },
    required: ['collectionName', 'itemId']
  }
};

export async function handleDeleteGridCardItem(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, itemId, filter, triggerWorkflows } = args;

    await client.deleteGridCardItem(collectionName, itemId, {
      filter,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card item deleted successfully: ${itemId}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error deleting grid card item: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 查看网格卡片项工具
 */
export const viewGridCardItemTool: Tool = {
  name: 'view_grid_card_item',
  description: 'View a specific grid card item',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      itemId: {
        type: ['string', 'number'],
        description: 'The ID of the item to view'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in the response'
      },
      appends: {
        type: 'array',
        items: { type: 'string' },
        description: 'Associated fields to include'
      },
      except: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to exclude from the response'
      }
    },
    required: ['collectionName', 'itemId']
  }
};

export async function handleViewGridCardItem(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, itemId, fields, appends, except } = args;

    const data = await client.viewGridCardItem(collectionName, itemId, {
      fields,
      appends,
      except
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card item retrieved successfully:\n${JSON.stringify({
            collection: collectionName,
            id: itemId,
            data
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error viewing grid card item: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 导出网格卡片数据工具
 */
export const exportGridCardDataTool: Tool = {
  name: 'export_grid_card_data',
  description: 'Export grid card data to Excel or CSV format',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions for export'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in export'
      },
      format: {
        type: 'string',
        enum: ['xlsx', 'csv'],
        description: 'Export format (default: xlsx)'
      }
    },
    required: ['collectionName']
  }
};

export async function handleExportGridCardData(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, filter, fields, format } = args;

    const result = await client.exportGridCardData(collectionName, {
      filter,
      fields,
      format
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card data exported successfully:\n${JSON.stringify({
            collection: collectionName,
            format: format || 'xlsx',
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error exporting grid card data: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 导入网格卡片数据工具
 */
export const importGridCardDataTool: Tool = {
  name: 'import_grid_card_data',
  description: 'Import data to grid card from Excel file',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      file: {
        type: 'string',
        description: 'The file to import (file path or base64 data)'
      },
      explain: {
        type: 'boolean',
        description: 'Whether to explain the import process'
      },
      dryRun: {
        type: 'boolean',
        description: 'Whether to perform a dry run (preview only)'
      }
    },
    required: ['collectionName', 'file']
  }
};

export async function handleImportGridCardData(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, file, explain, dryRun } = args;

    const result = await client.importGridCardData(collectionName, file, {
      explain,
      dryRun
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card data imported successfully:\n${JSON.stringify({
            collection: collectionName,
            explain: explain || false,
            dryRun: dryRun || false,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error importing grid card data: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加网格卡片操作按钮工具
 */
export const addGridCardActionTool: Tool = {
  name: 'add_grid_card_action',
  description: 'Add an action button to a grid card block',
  inputSchema: {
    type: 'object',
    properties: {
      gridCardUid: {
        type: 'string',
        description: 'The UID of the grid card block to add action to'
      },
      actionType: {
        type: 'string',
        enum: ['filter', 'addNew', 'refresh', 'import', 'export', 'customRequest', 'custom'],
        description: 'Type of action to add (use "custom" for custom configuration)'
      },
      customConfig: {
        type: 'object',
        properties: {
          title: { type: 'string' },
          action: { type: 'string' },
          icon: { type: 'string' },
          type: { type: 'string' },
          align: { type: 'string', enum: ['left', 'right'] },
          requiresACL: { type: 'boolean' },
          aclAction: { type: 'string' },
          settings: { type: 'string' }
        },
        description: 'Custom action configuration (required when actionType is "custom")'
      },
      position: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Position to insert the action (default: beforeEnd)'
      }
    },
    required: ['gridCardUid', 'actionType']
  }
};

export async function handleAddGridCardAction(client: NocoBaseClient, args: any) {
  try {
    const { gridCardUid, actionType, customConfig, position } = args;

    let actionConfig;
    if (actionType === 'custom') {
      if (!customConfig) {
        throw new Error('customConfig is required when actionType is "custom"');
      }
      actionConfig = customConfig;
    } else {
      actionConfig = GRID_CARD_ACTION_TEMPLATES[actionType as keyof typeof GRID_CARD_ACTION_TEMPLATES];
      if (!actionConfig) {
        throw new Error(`Unknown action type: ${actionType}`);
      }
    }

    const result = await client.addGridCardAction(gridCardUid, {
      ...actionConfig,
      position
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card action added successfully:\n${JSON.stringify({
            gridCardUid,
            actionType,
            title: actionConfig.title,
            action: actionConfig.action,
            position: position || 'beforeEnd'
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error adding grid card action: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 配置网格卡片项操作工具
 */
export const configureGridCardItemActionsTool: Tool = {
  name: 'configure_grid_card_item_actions',
  description: 'Configure action buttons for grid card items',
  inputSchema: {
    type: 'object',
    properties: {
      gridCardUid: {
        type: 'string',
        description: 'The UID of the grid card block'
      },
      actions: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            actionType: {
              type: 'string',
              enum: ['view', 'edit', 'delete', 'popup', 'updateRecord', 'customRequest', 'link', 'custom'],
              description: 'Type of action'
            },
            customConfig: {
              type: 'object',
              properties: {
                title: { type: 'string' },
                action: { type: 'string' },
                component: { type: 'string' },
                align: { type: 'string' },
                requiresACL: { type: 'boolean' },
                aclAction: { type: 'string' },
                settings: { type: 'string' },
                useComponentProps: { type: 'string' },
                componentProps: { type: 'object' }
              },
              description: 'Custom action configuration'
            }
          },
          required: ['actionType']
        },
        description: 'Array of actions to configure'
      }
    },
    required: ['gridCardUid', 'actions']
  }
};

export async function handleConfigureGridCardItemActions(client: NocoBaseClient, args: any) {
  try {
    const { gridCardUid, actions } = args;

    const actionConfigs = actions.map((action: any) => {
      if (action.actionType === 'custom') {
        return action.customConfig;
      } else {
        const template = GRID_CARD_ITEM_ACTION_TEMPLATES[action.actionType as keyof typeof GRID_CARD_ITEM_ACTION_TEMPLATES];
        if (!template) {
          throw new Error(`Unknown item action type: ${action.actionType}`);
        }
        return template;
      }
    });

    // 构建项操作栏Schema
    const itemActionBarUid = uid();
    const itemActionBarSchema: any = {
      type: 'void',
      'x-uid': itemActionBarUid,
      'x-component': 'ActionBar',
      'x-initializer': 'gridCard:configureItemActions',
      'x-use-component-props': 'useGridCardActionBarProps',
      'x-component-props': {
        layout: 'one-column',
      },
      properties: {}
    };

    // 添加每个操作
    actionConfigs.forEach((config: any, index: number) => {
      const actionUid = uid();
      itemActionBarSchema.properties[`action_${index}`] = {
        type: 'void',
        'x-uid': actionUid,
        title: config.title,
        'x-action': config.action,
        'x-component': config.component || 'Action.Link',
        'x-align': config.align || 'left',
        'x-use-component-props': config.useComponentProps || 'useGridCardItemActionProps',
        'x-component-props': config.componentProps || {},
      };

      if (config.requiresACL) {
        itemActionBarSchema.properties[`action_${index}`]['x-decorator'] = 'ACLActionProvider';
        itemActionBarSchema.properties[`action_${index}`]['x-acl-action'] = config.aclAction || config.action;
      }

      if (config.settings) {
        itemActionBarSchema.properties[`action_${index}`]['x-settings'] = config.settings;
      }
    });

    // 插入到网格卡片的项中
    const result = await client.insertAdjacentSchema(
      gridCardUid,
      itemActionBarSchema,
      'beforeEnd'
    );

    return {
      content: [
        {
          type: 'text',
          text: `Grid card item actions configured successfully:\n${JSON.stringify({
            gridCardUid,
            actionsCount: actions.length,
            actionTypes: actions.map((a: any) => a.actionType),
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring grid card item actions: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 配置网格卡片字段工具
 */
export const configureGridCardFieldsTool: Tool = {
  name: 'configure_grid_card_fields',
  description: 'Configure fields display in grid card items',
  inputSchema: {
    type: 'object',
    properties: {
      gridCardUid: {
        type: 'string',
        description: 'The UID of the grid card block'
      },
      fields: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            fieldName: { type: 'string' },
            title: { type: 'string' },
            component: { type: 'string' },
            componentProps: { type: 'object' },
            decorator: { type: 'string' },
            decoratorProps: { type: 'object' },
            gridProps: {
              type: 'object',
              properties: {
                xs: { type: 'number' },
                sm: { type: 'number' },
                md: { type: 'number' },
                lg: { type: 'number' },
                xl: { type: 'number' },
                xxl: { type: 'number' }
              }
            }
          },
          required: ['fieldName']
        },
        description: 'Array of fields to configure'
      }
    },
    required: ['gridCardUid', 'fields']
  }
};

export async function handleConfigureGridCardFields(client: NocoBaseClient, args: any) {
  try {
    const { gridCardUid, fields } = args;

    // 构建字段Schema
    const fieldsSchema: any = {};

    fields.forEach((field: any, index: number) => {
      const fieldUid = uid();
      const gridColUid = uid();

      fieldsSchema[`col_${index}`] = {
        type: 'void',
        'x-uid': gridColUid,
        'x-component': 'Grid.Col',
        'x-component-props': field.gridProps || { xs: 24, sm: 12, md: 8, lg: 6 },
        properties: {
          [field.fieldName]: {
            type: 'string',
            'x-uid': fieldUid,
            'x-collection-field': `${args.collectionName}.${field.fieldName}`,
            'x-component': field.component || 'CollectionField',
            'x-component-props': field.componentProps || {},
            'x-decorator': field.decorator || 'FormItem',
            'x-decorator-props': field.decoratorProps || {},
            'x-read-pretty': true,
            title: field.title || field.fieldName,
          }
        }
      };
    });

    // 更新网格卡片的字段配置
    const result = await client.patchSchema(gridCardUid, {
      properties: {
        list: {
          properties: {
            item: {
              properties: {
                grid: {
                  properties: fieldsSchema
                }
              }
            }
          }
        }
      }
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card fields configured successfully:\n${JSON.stringify({
            gridCardUid,
            fieldsCount: fields.length,
            fieldNames: fields.map((f: any) => f.fieldName),
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring grid card fields: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 筛选网格卡片工具
 */
export const filterGridCardTool: Tool = {
  name: 'filter_grid_card',
  description: 'Apply filter to grid card data',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions to apply'
      },
      page: {
        type: 'number',
        description: 'Page number (default: 1)'
      },
      pageSize: {
        type: 'number',
        description: 'Page size (default: 12)'
      },
      sort: {
        type: 'array',
        items: { type: 'string' },
        description: 'Sort fields'
      }
    },
    required: ['collectionName', 'filter']
  }
};

export async function handleFilterGridCard(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, filter, page, pageSize, sort } = args;

    const data = await client.getGridCardData(collectionName, {
      filter,
      page: page || 1,
      pageSize: pageSize || 12,
      sort
    });

    return {
      content: [
        {
          type: 'text',
          text: `Grid card filtered successfully:\n${JSON.stringify({
            collection: collectionName,
            filter,
            totalItems: data.meta?.count || 0,
            filteredItems: data.data?.length || 0,
            currentPage: data.meta?.page || 1
          }, null, 2)}\n\nFiltered Data:\n${JSON.stringify(data.data, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error filtering grid card: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 网格卡片自定义请求工具
 */
export const gridCardCustomRequestTool: Tool = {
  name: 'grid_card_custom_request',
  description: 'Send custom request for grid card operations',
  inputSchema: {
    type: 'object',
    properties: {
      requestId: {
        type: 'string',
        description: 'The ID of the custom request configuration'
      },
      data: {
        type: 'object',
        description: 'Data to send with the request'
      },
      currentRecord: {
        type: 'object',
        description: 'Current record context'
      },
      selectedRecords: {
        type: 'array',
        items: { type: 'object' },
        description: 'Selected records context'
      }
    },
    required: ['requestId']
  }
};

export async function handleGridCardCustomRequest(client: NocoBaseClient, args: any) {
  try {
    const { requestId, data, currentRecord, selectedRecords } = args;

    const requestData = {
      ...data,
      currentRecord,
      selectedRecords,
      $nForm: data,
      $nSelectedRecord: selectedRecords?.[0]
    };

    const result = await client.sendCustomRequest(requestId, requestData);

    return {
      content: [
        {
          type: 'text',
          text: `Grid card custom request sent successfully:\n${JSON.stringify({
            requestId,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error sending grid card custom request: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 注册所有网格卡片操作工具
 */
export async function registerGridCardOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 获取网格卡片数据
  server.registerTool(
    'get_grid_card_data',
    {
      title: 'Get Grid Card Data',
      description: 'Get grid card data with pagination, filtering, and sorting',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        page: z.number().optional().default(1).describe('Page number'),
        pageSize: z.number().optional().default(12).describe('Page size (default: 12 for grid cards)'),
        filter: z.any().optional().describe('Filter conditions'),
        fields: z.array(z.string()).optional().describe('Fields to include'),
        appends: z.array(z.string()).optional().describe('Associated fields to include'),
        sort: z.array(z.string()).optional().describe('Sort fields'),
        except: z.array(z.string()).optional().describe('Fields to exclude')
      }
    },
    async ({ collectionName, page, pageSize, filter, fields, appends, sort, except }: any) => {
      return await handleGetGridCardData(client, { collectionName, page, pageSize, filter, fields, appends, sort, except });
    }
  );

  // 创建网格卡片项
  server.registerTool(
    'create_grid_card_item',
    {
      title: 'Create Grid Card Item',
      description: 'Create a new grid card item',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        values: z.any().describe('The data for the new grid card item'),
        whitelist: z.array(z.string()).optional().describe('Fields allowed to be set'),
        blacklist: z.array(z.string()).optional().describe('Fields not allowed to be set'),
        updateAssociationValues: z.boolean().optional().describe('Whether to update association values'),
        triggerWorkflows: z.string().optional().describe('Workflow trigger configuration')
      }
    },
    async ({ collectionName, values, whitelist, blacklist, updateAssociationValues, triggerWorkflows }: any) => {
      return await handleCreateGridCardItem(client, { collectionName, values, whitelist, blacklist, updateAssociationValues, triggerWorkflows });
    }
  );

  // 更新网格卡片项
  server.registerTool(
    'update_grid_card_item',
    {
      title: 'Update Grid Card Item',
      description: 'Update a grid card item',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        itemId: z.union([z.string(), z.number()]).describe('The ID of the item to update'),
        values: z.any().describe('The updated data'),
        whitelist: z.array(z.string()).optional().describe('Fields allowed to be updated'),
        blacklist: z.array(z.string()).optional().describe('Fields not allowed to be updated'),
        updateAssociationValues: z.boolean().optional().describe('Whether to update association values'),
        forceUpdate: z.boolean().optional().describe('Whether to force update'),
        triggerWorkflows: z.string().optional().describe('Workflow trigger configuration')
      }
    },
    async ({ collectionName, itemId, values, whitelist, blacklist, updateAssociationValues, forceUpdate, triggerWorkflows }: any) => {
      return await handleUpdateGridCardItem(client, { collectionName, itemId, values, whitelist, blacklist, updateAssociationValues, forceUpdate, triggerWorkflows });
    }
  );

  // 删除网格卡片项
  server.registerTool(
    'delete_grid_card_item',
    {
      title: 'Delete Grid Card Item',
      description: 'Delete a grid card item',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        itemId: z.union([z.string(), z.number()]).describe('The ID of the item to delete'),
        filter: z.any().optional().describe('Additional filter conditions'),
        triggerWorkflows: z.string().optional().describe('Workflow trigger configuration')
      }
    },
    async ({ collectionName, itemId, filter, triggerWorkflows }: any) => {
      return await handleDeleteGridCardItem(client, { collectionName, itemId, filter, triggerWorkflows });
    }
  );

  // 查看网格卡片项
  server.registerTool(
    'view_grid_card_item',
    {
      title: 'View Grid Card Item',
      description: 'View a specific grid card item',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        itemId: z.union([z.string(), z.number()]).describe('The ID of the item to view'),
        fields: z.array(z.string()).optional().describe('Fields to include'),
        appends: z.array(z.string()).optional().describe('Associated fields to include'),
        except: z.array(z.string()).optional().describe('Fields to exclude')
      }
    },
    async ({ collectionName, itemId, fields, appends, except }: any) => {
      return await handleViewGridCardItem(client, { collectionName, itemId, fields, appends, except });
    }
  );

  // 导出网格卡片数据
  server.registerTool(
    'export_grid_card_data',
    {
      title: 'Export Grid Card Data',
      description: 'Export grid card data to Excel or CSV format',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        filter: z.any().optional().describe('Filter conditions for export'),
        fields: z.array(z.string()).optional().describe('Fields to include in export'),
        format: z.enum(['xlsx', 'csv']).optional().default('xlsx').describe('Export format')
      }
    },
    async ({ collectionName, filter, fields, format }: any) => {
      return await handleExportGridCardData(client, { collectionName, filter, fields, format });
    }
  );

  // 导入网格卡片数据
  server.registerTool(
    'import_grid_card_data',
    {
      title: 'Import Grid Card Data',
      description: 'Import data to grid card from Excel file',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        file: z.string().describe('The file to import (file path or base64 data)'),
        explain: z.boolean().optional().describe('Whether to explain the import process'),
        dryRun: z.boolean().optional().describe('Whether to perform a dry run (preview only)')
      }
    },
    async ({ collectionName, file, explain, dryRun }: any) => {
      return await handleImportGridCardData(client, { collectionName, file, explain, dryRun });
    }
  );

  // 添加网格卡片操作按钮
  server.registerTool(
    'add_grid_card_action',
    {
      title: 'Add Grid Card Action',
      description: 'Add an action button to a grid card block',
      inputSchema: {
        gridCardUid: z.string().describe('The UID of the grid card block'),
        actionType: z.enum(['filter', 'addNew', 'refresh', 'import', 'export', 'customRequest', 'custom']).describe('Type of action to add'),
        customConfig: z.object({
          title: z.string(),
          action: z.string(),
          icon: z.string().optional(),
          type: z.string().optional(),
          align: z.enum(['left', 'right']).optional(),
          requiresACL: z.boolean().optional(),
          aclAction: z.string().optional(),
          settings: z.string().optional()
        }).optional().describe('Custom action configuration (required when actionType is "custom")'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the action')
      }
    },
    async ({ gridCardUid, actionType, customConfig, position }: any) => {
      return await handleAddGridCardAction(client, { gridCardUid, actionType, customConfig, position });
    }
  );

  // 配置网格卡片项操作
  server.registerTool(
    'configure_grid_card_item_actions',
    {
      title: 'Configure Grid Card Item Actions',
      description: 'Configure action buttons for grid card items',
      inputSchema: {
        gridCardUid: z.string().describe('The UID of the grid card block'),
        actions: z.array(z.object({
          actionType: z.enum(['view', 'edit', 'delete', 'popup', 'updateRecord', 'customRequest', 'link', 'custom']).describe('Type of action'),
          customConfig: z.object({
            title: z.string(),
            action: z.string(),
            component: z.string().optional(),
            align: z.string().optional(),
            requiresACL: z.boolean().optional(),
            aclAction: z.string().optional(),
            settings: z.string().optional(),
            useComponentProps: z.string().optional(),
            componentProps: z.any().optional()
          }).optional().describe('Custom action configuration')
        })).describe('Array of actions to configure')
      }
    },
    async ({ gridCardUid, actions }: any) => {
      return await handleConfigureGridCardItemActions(client, { gridCardUid, actions });
    }
  );

  // 配置网格卡片字段
  server.registerTool(
    'configure_grid_card_fields',
    {
      title: 'Configure Grid Card Fields',
      description: 'Configure fields display in grid card items',
      inputSchema: {
        gridCardUid: z.string().describe('The UID of the grid card block'),
        fields: z.array(z.object({
          fieldName: z.string().describe('Name of the field'),
          title: z.string().optional().describe('Display title'),
          component: z.string().optional().describe('UI component'),
          componentProps: z.any().optional().describe('Component properties'),
          decorator: z.string().optional().describe('Field decorator'),
          decoratorProps: z.any().optional().describe('Decorator properties'),
          gridProps: z.object({
            xs: z.number().optional(),
            sm: z.number().optional(),
            md: z.number().optional(),
            lg: z.number().optional(),
            xl: z.number().optional(),
            xxl: z.number().optional()
          }).optional().describe('Grid column properties')
        })).describe('Array of fields to configure')
      }
    },
    async ({ gridCardUid, fields }: any) => {
      return await handleConfigureGridCardFields(client, { gridCardUid, fields });
    }
  );

  // 筛选网格卡片
  server.registerTool(
    'filter_grid_card',
    {
      title: 'Filter Grid Card',
      description: 'Apply filter to grid card data',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        filter: z.any().describe('Filter conditions to apply'),
        page: z.number().optional().default(1).describe('Page number'),
        pageSize: z.number().optional().default(12).describe('Page size'),
        sort: z.array(z.string()).optional().describe('Sort fields')
      }
    },
    async ({ collectionName, filter, page, pageSize, sort }: any) => {
      return await handleFilterGridCard(client, { collectionName, filter, page, pageSize, sort });
    }
  );

  // 网格卡片自定义请求
  server.registerTool(
    'grid_card_custom_request',
    {
      title: 'Grid Card Custom Request',
      description: 'Send custom request for grid card operations',
      inputSchema: {
        requestId: z.string().describe('The ID of the custom request configuration'),
        data: z.any().optional().describe('Data to send with the request'),
        currentRecord: z.any().optional().describe('Current record context'),
        selectedRecords: z.array(z.any()).optional().describe('Selected records context')
      }
    },
    async ({ requestId, data, currentRecord, selectedRecords }: any) => {
      return await handleGridCardCustomRequest(client, { requestId, data, currentRecord, selectedRecords });
    }
  );
}
