/**
 * NocoBase Kanban Operations Tools
 * 提供看板区块的操作功能，包括卡片CRUD、拖拽移动、筛选等
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';

/**
 * 获取看板数据工具
 */
export const getKanbanDataTool: Tool = {
  name: 'get_kanban_data',
  description: 'Get kanban data with grouping and sorting',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      groupField: {
        type: 'string',
        description: 'The field to group kanban cards by'
      },
      sortField: {
        type: 'string',
        description: 'The field to sort kanban cards by'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions for the kanban data'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in the response'
      },
      appends: {
        type: 'array',
        items: { type: 'string' },
        description: 'Associated fields to include'
      }
    },
    required: ['collectionName']
  }
};

export async function handleGetKanbanData(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, groupField, sortField, filter, fields, appends } = args;
    
    const data = await client.getKanbanData(collectionName, {
      groupField,
      sortField,
      filter,
      fields,
      appends
    });

    return {
      content: [
        {
          type: 'text',
          text: `Kanban data retrieved successfully:\n${JSON.stringify(data, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving kanban data: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 创建看板卡片工具
 */
export const createKanbanCardTool: Tool = {
  name: 'create_kanban_card',
  description: 'Create a new kanban card',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      cardData: {
        type: 'object',
        description: 'The data for the new card'
      },
      groupField: {
        type: 'string',
        description: 'The field to group kanban cards by'
      },
      columnValue: {
        type: ['string', 'number'],
        description: 'The value for the group field (which column to add to)'
      }
    },
    required: ['collectionName', 'cardData']
  }
};

export async function handleCreateKanbanCard(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, cardData, groupField, columnValue } = args;
    
    const result = await client.createKanbanCard(collectionName, cardData, groupField, columnValue);

    return {
      content: [
        {
          type: 'text',
          text: `Kanban card created successfully:\n${JSON.stringify(result, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating kanban card: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 更新看板卡片工具
 */
export const updateKanbanCardTool: Tool = {
  name: 'update_kanban_card',
  description: 'Update a kanban card',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      cardId: {
        type: ['string', 'number'],
        description: 'The ID of the card to update'
      },
      updateData: {
        type: 'object',
        description: 'The data to update'
      }
    },
    required: ['collectionName', 'cardId', 'updateData']
  }
};

export async function handleUpdateKanbanCard(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, cardId, updateData } = args;
    
    const result = await client.updateKanbanCard(collectionName, cardId, updateData);

    return {
      content: [
        {
          type: 'text',
          text: `Kanban card updated successfully:\n${JSON.stringify(result, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating kanban card: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 删除看板卡片工具
 */
export const deleteKanbanCardTool: Tool = {
  name: 'delete_kanban_card',
  description: 'Delete a kanban card',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      cardId: {
        type: ['string', 'number'],
        description: 'The ID of the card to delete'
      }
    },
    required: ['collectionName', 'cardId']
  }
};

export async function handleDeleteKanbanCard(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, cardId } = args;
    
    await client.deleteKanbanCard(collectionName, cardId);

    return {
      content: [
        {
          type: 'text',
          text: `Kanban card deleted successfully: ${cardId}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error deleting kanban card: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 移动看板卡片工具
 */
export const moveKanbanCardTool: Tool = {
  name: 'move_kanban_card',
  description: 'Move a kanban card between columns or positions',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      sourceId: {
        type: ['string', 'number'],
        description: 'The ID of the card to move'
      },
      targetId: {
        type: ['string', 'number'],
        description: 'The ID of the target card (optional)'
      },
      targetScope: {
        type: 'object',
        description: 'The target scope (e.g., {status: "done"} for moving to done column)'
      },
      sortField: {
        type: 'string',
        description: 'The sort field name'
      }
    },
    required: ['collectionName', 'sourceId']
  }
};

export async function handleMoveKanbanCard(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, sourceId, targetId, targetScope, sortField } = args;
    
    await client.moveKanbanCard(collectionName, {
      sourceId,
      targetId,
      targetScope,
      sortField
    });

    return {
      content: [
        {
          type: 'text',
          text: `Kanban card moved successfully: ${sourceId}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error moving kanban card: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 批量更新看板卡片工具
 */
export const batchUpdateKanbanCardsTool: Tool = {
  name: 'batch_update_kanban_cards',
  description: 'Batch update multiple kanban cards',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      cardIds: {
        type: 'array',
        items: { type: ['string', 'number'] },
        description: 'Array of card IDs to update'
      },
      updateData: {
        type: 'object',
        description: 'The data to update for all cards'
      }
    },
    required: ['collectionName', 'cardIds', 'updateData']
  }
};

export async function handleBatchUpdateKanbanCards(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, cardIds, updateData } = args;

    const results = await client.batchUpdateKanbanCards(collectionName, cardIds, updateData);

    return {
      content: [
        {
          type: 'text',
          text: `Batch update completed successfully. Updated ${results.length} cards:\n${JSON.stringify(results, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error in batch update: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加看板操作工具
 */
export const addKanbanActionTool: Tool = {
  name: 'add_kanban_action',
  description: 'Add an action button to a kanban block',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the kanban block'
      },
      actionType: {
        type: 'string',
        enum: ['filter', 'addNew', 'customRequest', 'export', 'refresh'],
        description: 'Type of action to add'
      },
      title: {
        type: 'string',
        description: 'Action button title'
      },
      icon: {
        type: 'string',
        description: 'Action button icon'
      },
      position: {
        type: 'string',
        enum: ['left', 'right'],
        description: 'Position of the action button'
      },
      customSettings: {
        type: 'object',
        description: 'Custom action settings (for custom actions)',
        properties: {
          url: { type: 'string' },
          method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE'] },
          headers: { type: 'object' },
          data: { type: 'object' }
        }
      }
    },
    required: ['blockUid', 'actionType', 'title']
  }
};

export async function handleAddKanbanAction(client: NocoBaseClient, args: any) {
  try {
    const { blockUid, actionType, title, icon, position = 'right', customSettings } = args;

    // 获取看板块的actions容器
    const blockSchema = await client.getSchemaProperties(blockUid);
    const actionsProperty = Object.values(blockSchema.properties || {}).find((prop: any) =>
      prop['x-component'] === 'ActionBar'
    ) as any;

    if (!actionsProperty) {
      throw new Error('Actions container not found in kanban block');
    }

    // 创建操作Schema
    let actionSchema: any;

    switch (actionType) {
      case 'filter':
        actionSchema = {
          type: 'void',
          title: title,
          'x-action': 'filter',
          'x-component': 'Filter.Action',
          'x-use-component-props': 'useFilterActionProps',
          'x-component-props': {
            icon: icon || 'FilterOutlined',
          },
          'x-align': position,
        };
        break;

      case 'addNew':
        actionSchema = {
          type: 'void',
          title: title,
          'x-action': 'create',
          'x-decorator': 'ACLActionProvider',
          'x-decorator-props': {
            skipScopeCheck: true,
          },
          'x-component': 'Action',
          'x-component-props': {
            icon: icon || 'PlusOutlined',
            openMode: 'drawer',
            type: 'primary',
          },
          'x-align': position,
        };
        break;

      case 'customRequest':
        actionSchema = {
          type: 'void',
          title: title,
          'x-action': 'customize:table:request:global',
          'x-component': 'Action',
          'x-component-props': {
            icon: icon || 'SettingOutlined',
            ...customSettings,
          },
          'x-align': position,
        };
        break;

      case 'export':
        actionSchema = {
          type: 'void',
          title: title,
          'x-action': 'export',
          'x-component': 'Action',
          'x-component-props': {
            icon: icon || 'ExportOutlined',
          },
          'x-align': position,
        };
        break;

      case 'refresh':
        actionSchema = {
          type: 'void',
          title: title,
          'x-action': 'refresh',
          'x-component': 'Action',
          'x-component-props': {
            icon: icon || 'ReloadOutlined',
          },
          'x-align': position,
        };
        break;

      default:
        throw new Error(`Unsupported action type: ${actionType}`);
    }

    // 插入操作到actions容器
    const result = await client.insertAdjacentSchema(
      actionsProperty['x-uid'],
      actionSchema,
      'beforeEnd'
    );

    return {
      content: [
        {
          type: 'text',
          text: `Kanban action added successfully:\n${JSON.stringify({
            actionType,
            title,
            uid: result['x-uid'],
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error adding kanban action: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 配置看板筛选器工具
 */
export const configureKanbanFilterTool: Tool = {
  name: 'configure_kanban_filter',
  description: 'Configure filter for a kanban block',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the kanban block'
      },
      filterConditions: {
        type: 'object',
        description: 'Filter conditions to apply'
      }
    },
    required: ['blockUid', 'filterConditions']
  }
};

export async function handleConfigureKanbanFilter(client: NocoBaseClient, args: any) {
  try {
    const { blockUid, filterConditions } = args;

    // 更新看板块的筛选参数
    const updates = {
      'x-decorator-props': {
        params: {
          filter: filterConditions
        }
      }
    };

    await client.patchSchema(blockUid, updates);

    return {
      content: [
        {
          type: 'text',
          text: `Kanban filter configured successfully:\n${JSON.stringify(filterConditions, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring kanban filter: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 配置看板分组字段工具
 */
export const configureKanbanGroupFieldTool: Tool = {
  name: 'configure_kanban_group_field',
  description: 'Configure the group field for a kanban block',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the kanban block'
      },
      groupField: {
        type: 'string',
        description: 'The field name to group kanban cards by'
      }
    },
    required: ['blockUid', 'groupField']
  }
};

export async function handleConfigureKanbanGroupField(client: NocoBaseClient, args: any) {
  try {
    const { blockUid, groupField } = args;

    // 更新看板块的分组字段
    const updates = {
      'x-decorator-props': {
        groupField
      }
    };

    await client.patchSchema(blockUid, updates);

    return {
      content: [
        {
          type: 'text',
          text: `Kanban group field configured successfully: ${groupField}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring kanban group field: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 配置看板排序字段工具
 */
export const configureKanbanSortFieldTool: Tool = {
  name: 'configure_kanban_sort_field',
  description: 'Configure the sort field for a kanban block',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the kanban block'
      },
      sortField: {
        type: 'string',
        description: 'The field name to sort kanban cards by'
      }
    },
    required: ['blockUid', 'sortField']
  }
};

export async function handleConfigureKanbanSortField(client: NocoBaseClient, args: any) {
  try {
    const { blockUid, sortField } = args;

    // 更新看板块的排序字段
    const updates = {
      'x-decorator-props': {
        sortField,
        params: {
          sort: [sortField]
        }
      }
    };

    await client.patchSchema(blockUid, updates);

    return {
      content: [
        {
          type: 'text',
          text: `Kanban sort field configured successfully: ${sortField}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring kanban sort field: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 创建排序字段工具
 */
export const createSortFieldTool: Tool = {
  name: 'create_sort_field',
  description: 'Create a sort field for kanban ordering',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      fieldName: {
        type: 'string',
        description: 'The name of the sort field'
      },
      fieldTitle: {
        type: 'string',
        description: 'The title of the sort field'
      },
      scopeKey: {
        type: 'string',
        description: 'The scope key for the sort field (optional)'
      }
    },
    required: ['collectionName', 'fieldName']
  }
};

export async function handleCreateSortField(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, fieldName, fieldTitle, scopeKey } = args;

    const fieldData = {
      name: fieldName,
      title: fieldTitle || fieldName,
      type: 'sort',
      interface: 'sort',
      scopeKey
    };

    const result = await client.createSortField(collectionName, fieldData);

    return {
      content: [
        {
          type: 'text',
          text: `Sort field created successfully:\n${JSON.stringify(result, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating sort field: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 注册所有看板操作工具
 */
export async function registerKanbanOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 获取看板数据工具
  server.registerTool(
    'get_kanban_data',
    {
      title: 'Get Kanban Data',
      description: 'Get kanban data with grouping and sorting',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        groupField: z.string().optional().describe('The field to group kanban cards by'),
        sortField: z.string().optional().describe('The field to sort kanban cards by'),
        filter: z.any().optional().describe('Filter conditions for the kanban data'),
        fields: z.array(z.string()).optional().describe('Fields to include in the response'),
        appends: z.array(z.string()).optional().describe('Associated fields to include')
      }
    },
    async (args: any) => {
      return await handleGetKanbanData(client, args);
    }
  );

  // 创建看板卡片工具
  server.registerTool(
    'create_kanban_card',
    {
      title: 'Create Kanban Card',
      description: 'Create a new kanban card',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        cardData: z.any().describe('The data for the new card'),
        groupField: z.string().optional().describe('The field to group kanban cards by'),
        columnValue: z.union([z.string(), z.number()]).optional().describe('The value for the group field (which column to add to)')
      }
    },
    async (args: any) => {
      return await handleCreateKanbanCard(client, args);
    }
  );

  // 更新看板卡片工具
  server.registerTool(
    'update_kanban_card',
    {
      title: 'Update Kanban Card',
      description: 'Update a kanban card',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        cardId: z.union([z.string(), z.number()]).describe('The ID of the card to update'),
        updateData: z.any().describe('The data to update')
      }
    },
    async (args: any) => {
      return await handleUpdateKanbanCard(client, args);
    }
  );

  // 删除看板卡片工具
  server.registerTool(
    'delete_kanban_card',
    {
      title: 'Delete Kanban Card',
      description: 'Delete a kanban card',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        cardId: z.union([z.string(), z.number()]).describe('The ID of the card to delete')
      }
    },
    async (args: any) => {
      return await handleDeleteKanbanCard(client, args);
    }
  );

  // 移动看板卡片工具
  server.registerTool(
    'move_kanban_card',
    {
      title: 'Move Kanban Card',
      description: 'Move a kanban card between columns or positions',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        sourceId: z.union([z.string(), z.number()]).describe('The ID of the card to move'),
        targetId: z.union([z.string(), z.number()]).optional().describe('The ID of the target card (optional)'),
        targetScope: z.any().optional().describe('The target scope (e.g., {status: "done"} for moving to done column)'),
        sortField: z.string().optional().describe('The sort field name')
      }
    },
    async (args: any) => {
      return await handleMoveKanbanCard(client, args);
    }
  );

  // 批量更新看板卡片工具
  server.registerTool(
    'batch_update_kanban_cards',
    {
      title: 'Batch Update Kanban Cards',
      description: 'Batch update multiple kanban cards',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        cardIds: z.array(z.union([z.string(), z.number()])).describe('Array of card IDs to update'),
        updateData: z.any().describe('The data to update for all cards')
      }
    },
    async (args: any) => {
      return await handleBatchUpdateKanbanCards(client, args);
    }
  );

  // 添加看板操作工具
  server.registerTool(
    'add_kanban_action',
    {
      title: 'Add Kanban Action',
      description: 'Add an action button to a kanban block',
      inputSchema: {
        blockUid: z.string().describe('The UID of the kanban block'),
        actionType: z.enum(['filter', 'addNew', 'customRequest', 'export', 'refresh']).describe('Type of action to add'),
        title: z.string().describe('Action button title'),
        icon: z.string().optional().describe('Action button icon'),
        position: z.enum(['left', 'right']).optional().default('right').describe('Position of the action button'),
        customSettings: z.object({
          url: z.string().optional(),
          method: z.enum(['GET', 'POST', 'PUT', 'DELETE']).optional(),
          headers: z.any().optional(),
          data: z.any().optional()
        }).optional().describe('Custom action settings (for custom actions)')
      }
    },
    async (args: any) => {
      return await handleAddKanbanAction(client, args);
    }
  );

  // 配置看板筛选器工具
  server.registerTool(
    'configure_kanban_filter',
    {
      title: 'Configure Kanban Filter',
      description: 'Configure filter for a kanban block',
      inputSchema: {
        blockUid: z.string().describe('The UID of the kanban block'),
        filterConditions: z.any().describe('Filter conditions to apply')
      }
    },
    async (args: any) => {
      return await handleConfigureKanbanFilter(client, args);
    }
  );

  // 配置看板分组字段工具
  server.registerTool(
    'configure_kanban_group_field',
    {
      title: 'Configure Kanban Group Field',
      description: 'Configure the group field for a kanban block',
      inputSchema: {
        blockUid: z.string().describe('The UID of the kanban block'),
        groupField: z.string().describe('The field name to group kanban cards by')
      }
    },
    async (args: any) => {
      return await handleConfigureKanbanGroupField(client, args);
    }
  );

  // 配置看板排序字段工具
  server.registerTool(
    'configure_kanban_sort_field',
    {
      title: 'Configure Kanban Sort Field',
      description: 'Configure the sort field for a kanban block',
      inputSchema: {
        blockUid: z.string().describe('The UID of the kanban block'),
        sortField: z.string().describe('The field name to sort kanban cards by')
      }
    },
    async (args: any) => {
      return await handleConfigureKanbanSortField(client, args);
    }
  );

  // 创建排序字段工具
  server.registerTool(
    'create_sort_field',
    {
      title: 'Create Sort Field',
      description: 'Create a sort field for kanban ordering',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        fieldName: z.string().describe('The name of the sort field'),
        fieldTitle: z.string().optional().describe('The title of the sort field'),
        scopeKey: z.string().optional().describe('The scope key for the sort field (optional)')
      }
    },
    async (args: any) => {
      return await handleCreateSortField(client, args);
    }
  );
}
