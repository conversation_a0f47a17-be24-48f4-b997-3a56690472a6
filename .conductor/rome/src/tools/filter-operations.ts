/**
 * NocoBase Filter Operations Tools
 * 提供筛选区块的操作功能
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';

/**
 * 添加筛选表单区块工具
 */
export const addFilterFormBlockTool: Tool = {
  name: 'add_filter_form_block',
  description: 'Add a filter form block to a page',
  inputSchema: {
    type: 'object',
    properties: {
      parentUid: {
        type: 'string',
        description: 'The UID of the parent container (usually a Grid)'
      },
      collectionName: {
        type: 'string',
        description: 'The name of the collection to filter'
      },
      title: {
        type: 'string',
        description: 'The title of the filter block',
        default: 'Filter'
      },
      filterTargets: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            uid: { type: 'string', description: 'Target block UID' },
            field: { type: 'string', description: 'Associated field name' }
          },
          required: ['uid']
        },
        description: 'Filter targets (blocks to be filtered)'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in the filter form'
      },
      position: {
        type: 'string',
        description: 'Position to insert the block',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        default: 'beforeEnd'
      }
    },
    required: ['parentUid', 'collectionName']
  }
};

export async function handleAddFilterFormBlock(client: NocoBaseClient, args: any) {
  try {
    const {
      parentUid,
      collectionName,
      title = 'Filter',
      filterTargets = [],
      fields = [],
      position = 'beforeEnd'
    } = args;

    const result = await client.addFilterFormBlock({
      parentUid,
      collectionName,
      title,
      filterTargets,
      fields,
      position
    });

    return {
      content: [
        {
          type: 'text',
          text: `Filter form block created successfully:\n${JSON.stringify({
            uid: result.uid,
            title,
            collectionName,
            filterTargets,
            fieldsCount: fields.length,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating filter form block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加关联筛选区块工具
 */
export const addAssociationFilterBlockTool: Tool = {
  name: 'add_association_filter_block',
  description: 'Add an association filter block to a page',
  inputSchema: {
    type: 'object',
    properties: {
      parentUid: {
        type: 'string',
        description: 'The UID of the parent container (usually a Grid)'
      },
      collectionName: {
        type: 'string',
        description: 'The name of the collection to filter'
      },
      associationField: {
        type: 'string',
        description: 'The association field name'
      },
      title: {
        type: 'string',
        description: 'The title of the filter block'
      },
      filterTargets: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            uid: { type: 'string', description: 'Target block UID' },
            field: { type: 'string', description: 'Associated field name' }
          },
          required: ['uid']
        },
        description: 'Filter targets (blocks to be filtered)'
      },
      position: {
        type: 'string',
        description: 'Position to insert the block',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        default: 'beforeEnd'
      }
    },
    required: ['parentUid', 'collectionName', 'associationField']
  }
};

export async function handleAddAssociationFilterBlock(client: NocoBaseClient, args: any) {
  try {
    const {
      parentUid,
      collectionName,
      associationField,
      title,
      filterTargets = [],
      position = 'beforeEnd'
    } = args;

    const result = await client.addAssociationFilterBlock({
      parentUid,
      collectionName,
      associationField,
      title,
      filterTargets,
      position
    });

    return {
      content: [
        {
          type: 'text',
          text: `Association filter block created successfully:\n${JSON.stringify({
            uid: result.uid,
            title,
            collectionName,
            associationField,
            filterTargets,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating association filter block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 配置筛选目标工具
 */
export const configureFilterTargetsTool: Tool = {
  name: 'configure_filter_targets',
  description: 'Configure filter targets for a filter block',
  inputSchema: {
    type: 'object',
    properties: {
      filterBlockUid: {
        type: 'string',
        description: 'The UID of the filter block'
      },
      targets: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            uid: { type: 'string', description: 'Target block UID' },
            field: { type: 'string', description: 'Associated field name' }
          },
          required: ['uid']
        },
        description: 'Filter targets to configure'
      }
    },
    required: ['filterBlockUid', 'targets']
  }
};

export async function handleConfigureFilterTargets(client: NocoBaseClient, args: any) {
  try {
    const { filterBlockUid, targets } = args;

    await client.configureFilterTargets(filterBlockUid, targets);

    return {
      content: [
        {
          type: 'text',
          text: `Filter targets configured successfully:\n${JSON.stringify({
            filterBlockUid,
            targets,
            targetsCount: targets.length
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring filter targets: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加筛选字段工具
 */
export const addFilterFieldTool: Tool = {
  name: 'add_filter_field',
  description: 'Add a filter field to a filter form block',
  inputSchema: {
    type: 'object',
    properties: {
      filterBlockUid: {
        type: 'string',
        description: 'The UID of the filter block'
      },
      fieldName: {
        type: 'string',
        description: 'The name of the field to add'
      },
      operator: {
        type: 'string',
        description: 'Filter operator',
        enum: ['$eq', '$ne', '$gt', '$gte', '$lt', '$lte', '$in', '$notIn', '$like', '$notLike', '$null', '$notNull', '$between', '$dateBetween'],
        default: '$eq'
      },
      title: {
        type: 'string',
        description: 'Field title (optional, will use field display name if not provided)'
      },
      required: {
        type: 'boolean',
        description: 'Whether the field is required',
        default: false
      },
      position: {
        type: 'string',
        description: 'Position to insert the field',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        default: 'beforeEnd'
      }
    },
    required: ['filterBlockUid', 'fieldName']
  }
};

export async function handleAddFilterField(client: NocoBaseClient, args: any) {
  try {
    const {
      filterBlockUid,
      fieldName,
      operator = '$eq',
      title,
      required = false,
      position = 'beforeEnd'
    } = args;

    const result = await client.addFilterField({
      filterBlockUid,
      fieldName,
      operator,
      title,
      required,
      position
    });

    return {
      content: [
        {
          type: 'text',
          text: `Filter field added successfully:\n${JSON.stringify({
            filterBlockUid,
            fieldName,
            operator,
            title,
            required,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error adding filter field: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 执行筛选操作工具
 */
export const executeFilterTool: Tool = {
  name: 'execute_filter',
  description: 'Execute filter operation on target blocks',
  inputSchema: {
    type: 'object',
    properties: {
      filterBlockUid: {
        type: 'string',
        description: 'The UID of the filter block'
      },
      filterValues: {
        type: 'object',
        description: 'Filter values to apply'
      },
      operators: {
        type: 'object',
        description: 'Operators for each field (field -> operator mapping)'
      }
    },
    required: ['filterBlockUid', 'filterValues']
  }
};

export async function handleExecuteFilter(client: NocoBaseClient, args: any) {
  try {
    const { filterBlockUid, filterValues, operators = {} } = args;

    const result = await client.executeFilter({
      filterBlockUid,
      filterValues,
      operators
    });

    return {
      content: [
        {
          type: 'text',
          text: `Filter executed successfully:\n${JSON.stringify({
            filterBlockUid,
            filterValues,
            operators,
            affectedBlocks: result.affectedBlocks || 0
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error executing filter: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 重置筛选操作工具
 */
export const resetFilterTool: Tool = {
  name: 'reset_filter',
  description: 'Reset filter values and clear filtering on target blocks',
  inputSchema: {
    type: 'object',
    properties: {
      filterBlockUid: {
        type: 'string',
        description: 'The UID of the filter block'
      },
      clearDefaultValue: {
        type: 'boolean',
        description: 'Whether to clear default values',
        default: false
      }
    },
    required: ['filterBlockUid']
  }
};

export async function handleResetFilter(client: NocoBaseClient, args: any) {
  try {
    const { filterBlockUid, clearDefaultValue = false } = args;

    const result = await client.resetFilter({
      filterBlockUid,
      clearDefaultValue
    });

    return {
      content: [
        {
          type: 'text',
          text: `Filter reset successfully:\n${JSON.stringify({
            filterBlockUid,
            clearDefaultValue,
            affectedBlocks: result.affectedBlocks || 0
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error resetting filter: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 获取筛选条件工具
 */
export const getFilterConditionsTool: Tool = {
  name: 'get_filter_conditions',
  description: 'Get current filter conditions from a filter block',
  inputSchema: {
    type: 'object',
    properties: {
      filterBlockUid: {
        type: 'string',
        description: 'The UID of the filter block'
      }
    },
    required: ['filterBlockUid']
  }
};

export async function handleGetFilterConditions(client: NocoBaseClient, args: any) {
  try {
    const { filterBlockUid } = args;

    const conditions = await client.getFilterConditions(filterBlockUid);

    return {
      content: [
        {
          type: 'text',
          text: `Filter conditions retrieved successfully:\n${JSON.stringify({
            filterBlockUid,
            conditions,
            fieldsCount: Object.keys(conditions.values || {}).length
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving filter conditions: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 设置筛选默认值工具
 */
export const setFilterDefaultValuesTool: Tool = {
  name: 'set_filter_default_values',
  description: 'Set default values for filter fields',
  inputSchema: {
    type: 'object',
    properties: {
      filterBlockUid: {
        type: 'string',
        description: 'The UID of the filter block'
      },
      defaultValues: {
        type: 'object',
        description: 'Default values for filter fields (field -> value mapping)'
      }
    },
    required: ['filterBlockUid', 'defaultValues']
  }
};

export async function handleSetFilterDefaultValues(client: NocoBaseClient, args: any) {
  try {
    const { filterBlockUid, defaultValues } = args;

    await client.setFilterDefaultValues(filterBlockUid, defaultValues);

    return {
      content: [
        {
          type: 'text',
          text: `Filter default values set successfully:\n${JSON.stringify({
            filterBlockUid,
            defaultValues,
            fieldsCount: Object.keys(defaultValues).length
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error setting filter default values: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 注册所有筛选操作工具
 */
export async function registerFilterOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 添加筛选表单区块工具
  server.registerTool(
    'add_filter_form_block',
    {
      title: 'Add Filter Form Block',
      description: 'Add a filter form block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection to filter'),
        title: z.string().optional().default('Filter').describe('The title of the filter block'),
        filterTargets: z.array(z.object({
          uid: z.string().describe('Target block UID'),
          field: z.string().optional().describe('Associated field name')
        })).optional().describe('Filter targets (blocks to be filtered)'),
        fields: z.array(z.string()).optional().describe('Fields to include in the filter form'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddFilterFormBlock(client, args);
    }
  );

  // 添加关联筛选区块工具
  server.registerTool(
    'add_association_filter_block',
    {
      title: 'Add Association Filter Block',
      description: 'Add an association filter block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection to filter'),
        associationField: z.string().describe('The association field name'),
        title: z.string().optional().describe('The title of the filter block'),
        filterTargets: z.array(z.object({
          uid: z.string().describe('Target block UID'),
          field: z.string().optional().describe('Associated field name')
        })).optional().describe('Filter targets (blocks to be filtered)'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddAssociationFilterBlock(client, args);
    }
  );

  // 配置筛选目标工具
  server.registerTool(
    'configure_filter_targets',
    {
      title: 'Configure Filter Targets',
      description: 'Configure filter targets for a filter block',
      inputSchema: {
        filterBlockUid: z.string().describe('The UID of the filter block'),
        targets: z.array(z.object({
          uid: z.string().describe('Target block UID'),
          field: z.string().optional().describe('Associated field name')
        })).describe('Filter targets to configure')
      }
    },
    async (args: any) => {
      return await handleConfigureFilterTargets(client, args);
    }
  );

  // 添加筛选字段工具
  server.registerTool(
    'add_filter_field',
    {
      title: 'Add Filter Field',
      description: 'Add a filter field to a filter form block',
      inputSchema: {
        filterBlockUid: z.string().describe('The UID of the filter block'),
        fieldName: z.string().describe('The name of the field to add'),
        operator: z.enum(['$eq', '$ne', '$gt', '$gte', '$lt', '$lte', '$in', '$notIn', '$like', '$notLike', '$null', '$notNull', '$between', '$dateBetween']).optional().default('$eq').describe('Filter operator'),
        title: z.string().optional().describe('Field title (optional, will use field display name if not provided)'),
        required: z.boolean().optional().default(false).describe('Whether the field is required'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the field')
      }
    },
    async (args: any) => {
      return await handleAddFilterField(client, args);
    }
  );

  // 执行筛选操作工具
  server.registerTool(
    'execute_filter',
    {
      title: 'Execute Filter',
      description: 'Execute filter operation on target blocks',
      inputSchema: {
        filterBlockUid: z.string().describe('The UID of the filter block'),
        filterValues: z.any().describe('Filter values to apply'),
        operators: z.any().optional().describe('Operators for each field (field -> operator mapping)')
      }
    },
    async (args: any) => {
      return await handleExecuteFilter(client, args);
    }
  );

  // 重置筛选操作工具
  server.registerTool(
    'reset_filter',
    {
      title: 'Reset Filter',
      description: 'Reset filter values and clear filtering on target blocks',
      inputSchema: {
        filterBlockUid: z.string().describe('The UID of the filter block'),
        clearDefaultValue: z.boolean().optional().default(false).describe('Whether to clear default values')
      }
    },
    async (args: any) => {
      return await handleResetFilter(client, args);
    }
  );

  // 获取筛选条件工具
  server.registerTool(
    'get_filter_conditions',
    {
      title: 'Get Filter Conditions',
      description: 'Get current filter conditions from a filter block',
      inputSchema: {
        filterBlockUid: z.string().describe('The UID of the filter block')
      }
    },
    async (args: any) => {
      return await handleGetFilterConditions(client, args);
    }
  );

  // 设置筛选默认值工具
  server.registerTool(
    'set_filter_default_values',
    {
      title: 'Set Filter Default Values',
      description: 'Set default values for filter fields',
      inputSchema: {
        filterBlockUid: z.string().describe('The UID of the filter block'),
        defaultValues: z.any().describe('Default values for filter fields (field -> value mapping)')
      }
    },
    async (args: any) => {
      return await handleSetFilterDefaultValues(client, args);
    }
  );
}
