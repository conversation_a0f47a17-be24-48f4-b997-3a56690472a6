#!/usr/bin/env node

// 深度对比所有页面的详细配置
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let allRoutes = [];
  let currentStep = 0;
  let page1Route = null;
  let page1TabRoute = null;
  let newPageRoute = null;
  let newPageTabRoute = null;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          currentStep = 1;
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } 
        else if (msg.id === 2 && currentStep === 1) {
          let raw = ''; for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          const payload = JSON.parse(raw); 
          allRoutes = payload?.data || payload || [];
          
          // 找到所有相关页面
          function findRoutes(nodes = []) {
            for (const n of nodes) {
              if (n.title === 'page_1') {
                page1Route = n;
                if (n.children && n.children.length > 0) {
                  page1TabRoute = n.children[0];
                }
              }
              if (n.title && n.title.includes('正确的 MCP 页面 1754636822273')) {
                newPageRoute = n;
                if (n.children && n.children.length > 0) {
                  newPageTabRoute = n.children[0];
                }
              }
              if (Array.isArray(n.children)) findRoutes(n.children);
            }
          }
          findRoutes(allRoutes);
          
          console.log('=== 找到的路由 ===');
          console.log('page_1:', page1Route ? 'Found' : 'Not found');
          console.log('page_1 tab:', page1TabRoute ? 'Found' : 'Not found');
          console.log('新页面:', newPageRoute ? 'Found' : 'Not found');
          console.log('新页面 tab:', newPageTabRoute ? 'Found' : 'Not found');
          
          if (!page1Route || !newPageRoute) {
            console.log('缺少必要的路由');
            server.kill();
            return;
          }
          
          // 对比路由配置
          console.log('\n=== page_1 路由配置 ===');
          console.log(JSON.stringify(page1Route, null, 2));
          
          console.log('\n=== 新页面路由配置 ===');
          console.log(JSON.stringify(newPageRoute, null, 2));
          
          if (page1TabRoute) {
            console.log('\n=== page_1 tab 路由配置 ===');
            console.log(JSON.stringify(page1TabRoute, null, 2));
          }
          
          if (newPageTabRoute) {
            console.log('\n=== 新页面 tab 路由配置 ===');
            console.log(JSON.stringify(newPageTabRoute, null, 2));
          }
          
          // 获取 page_1 的 schema properties
          currentStep = 2;
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'get_schema_properties', arguments: { schemaUid: page1Route.schemaUid } } });
        }
        else if (msg.id === 3 && currentStep === 2) {
          console.log('\n=== page_1 主页面 Schema Properties ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 获取新页面的 schema properties
          currentStep = 3;
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'get_schema_properties', arguments: { schemaUid: newPageRoute.schemaUid } } });
        }
        else if (msg.id === 4 && currentStep === 3) {
          console.log('\n=== 新页面主页面 Schema Properties ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          if (page1TabRoute) {
            // 获取 page_1 tab 的 schema properties
            currentStep = 4;
            send(server, { jsonrpc: '2.0', id: 5, method: 'tools/call', params: { name: 'get_schema_properties', arguments: { schemaUid: page1TabRoute.schemaUid } } });
          } else {
            server.kill();
          }
        }
        else if (msg.id === 5 && currentStep === 4) {
          console.log('\n=== page_1 tab Schema Properties ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          if (newPageTabRoute) {
            // 获取新页面 tab 的 schema properties
            currentStep = 5;
            send(server, { jsonrpc: '2.0', id: 6, method: 'tools/call', params: { name: 'get_schema_properties', arguments: { schemaUid: newPageTabRoute.schemaUid } } });
          } else {
            server.kill();
          }
        }
        else if (msg.id === 6 && currentStep === 5) {
          console.log('\n=== 新页面 tab Schema Properties ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'deep-compare', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
