#!/usr/bin/env node

// 检查系统权限和配置
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let step = 0;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          step = 1;
          console.log('=== 检查当前用户信息 ===');
          // 尝试获取当前用户信息
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'list_collections', arguments: {} } });
        } 
        else if (msg.id === 2 && step === 1) {
          console.log('Collections 列表（验证权限）:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text.substring(0, 500) + '...');
          
          step = 2;
          console.log('\n=== 尝试直接向 page_1 的 tabs 添加区块 ===');
          // 直接尝试向 page_1 的 tabs 子路由添加一个 Markdown 区块
          send(server, { 
            jsonrpc: '2.0', 
            id: 3, 
            method: 'tools/call', 
            params: { 
              name: 'add_markdown_block', 
              arguments: { 
                parentUid: 'c7owgwgeww4', // page_1 的 tabs schemaUid
                title: '测试区块',
                content: '# 测试\n\n如果你能看到这个区块，说明添加成功了。'
              } 
            } 
          });
        }
        else if (msg.id === 3 && step === 2) {
          console.log('向 page_1 添加区块的结果:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 3;
          console.log('\n=== 验证区块是否添加成功 ===');
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'list_page_blocks', arguments: { schemaUid: 'c7owgwgeww4' } } });
        }
        else if (msg.id === 4 && step === 3) {
          console.log('page_1 tabs 的区块列表:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 4;
          console.log('\n=== 检查 page_1 tabs 的最新 Properties ===');
          send(server, { jsonrpc: '2.0', id: 5, method: 'tools/call', params: { name: 'get_schema_properties', arguments: { schemaUid: 'c7owgwgeww4' } } });
        }
        else if (msg.id === 5 && step === 4) {
          console.log('page_1 tabs 的最新 Properties:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n=== 总结 ===');
          console.log('1. 如果区块添加成功，说明 API 层面没问题');
          console.log('2. 如果区块添加失败，说明可能有权限或其他限制');
          console.log('3. 请检查 page_1 页面是否现在显示了新添加的区块');
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'check-permissions', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
